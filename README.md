# سوق الجملة للخضر والغلال - نقطة بيع 14

## نظام إدارة شامل لسوق الجملة

### 📋 وصف المشروع

برنامج سطح مكتب متكامل لإدارة سوق الجملة للخضر والغلال بجرزونة - نقطة بيع عدد 14 (بيه الغالي). يوفر النظام إدارة شاملة للمشتريات، الحرفاء، الموردين، البضائع، والصناديق مع نظام حسابي دقيق للأوزان والرهن والتكاليف.

### ✨ المميزات الرئيسية

- **لوحة تحكم شاملة**: عرض ملخص الديون، الرهن، وعدد الصناديق
- **إدارة الحرفاء**: تسجيل الحرفاء مع تتبع حالات الدفع (مدفوع/تقسيط/دين)
- **إدارة الموردين**: تسجيل الموردين مع ربطهم بالبضائع والمشتريات
- **إدارة البضائع**: تتبع البضائع الواردة والمباعة والمتبقية
- **إدارة الصناديق**: تعريف أنواع الصناديق مع أوزانها ورهنها
- **نظام المشتريات**: تسجيل عمليات الشراء مع الحسابات التلقائية
- **فواتير الموردين**: استخراج وطباعة فواتير تلقائية
- **بحث ذكي**: بحث سريع في جميع البيانات
- **تصميم متجاوب**: واجهة أنيقة تعمل على جميع الأجهزة

### 🔧 التقنيات المستخدمة

- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Database**: IndexedDB (قاعدة بيانات محلية)
- **UI Framework**: CSS Grid & Flexbox
- **Icons**: Font Awesome
- **Fonts**: Google Fonts (Cairo)

### 📁 هيكل المشروع

```
├── index.html              # الصفحة الرئيسية
├── styles/
│   └── main.css            # ملف الأنماط الرئيسي
├── js/
│   ├── database.js         # إدارة قاعدة البيانات
│   ├── utils.js           # الوظائف المساعدة
│   └── main.js            # الملف الرئيسي للتطبيق
└── README.md              # ملف التوثيق
```

### 🚀 كيفية التشغيل

1. **تحميل المشروع**:
   ```bash
   git clone [repository-url]
   cd market-management-system
   ```

2. **تشغيل الخادم المحلي**:
   ```bash
   python -m http.server 8000
   ```
   أو
   ```bash
   npx serve .
   ```

3. **فتح التطبيق**:
   افتح المتصفح وانتقل إلى `http://localhost:8000`

### 💰 النظام الحسابي

#### حساب الأوزان:
- **الوزن الصافي** = الوزن القائم - (عدد الصناديق × وزن الصندوق الفارغ)

#### حساب الرهن:
- **الصندوق الكبير وPlato**: 10 دنانير لكل صندوق
- **باقي الصناديق**: 3 دنانير لكل صندوق

#### حساب التكلفة:
- **تكلفة الحمولة** = عدد الصناديق × حمولة الصندوق
- **في حالة "بلا حمولة"**: الوزن الصافي × 0.01 دينار

#### حالات الدفع:
- **مدفوع**: دفع كامل المبلغ
- **تقسيط**: دفع جزء من المبلغ
- **دين**: لم يدفع أي مبلغ

### 📊 القوائم والوظائف

#### 1. لوحة التحكم (Dashboard)
- عرض إجمالي الديون والرهن
- عدد الصناديق ومبيعات اليوم
- جدول آخر المشتريات

#### 2. إدارة الحرفاء
- إضافة وتعديل وحذف الحرفاء
- عرض حالة الدفع لكل حريف
- بحث ذكي في بيانات الحرفاء

#### 3. إدارة الموردين
- إضافة وتعديل وحذف الموردين
- عرض إحصائيات المبيعات لكل مورد
- ربط الموردين بالبضائع

#### 4. إدارة البضائع
- تتبع البضائع الواردة والمباعة
- ربط البضائع بالموردين
- عرض الكميات المتبقية

#### 5. إدارة الصناديق
- تعريف أنواع الصناديق المختلفة
- تحديد الأوزان والرهن والحمولة
- استخدام في حسابات المشتريات

#### 6. نظام المشتريات
- تسجيل عمليات الشراء
- حسابات تلقائية للأوزان والتكاليف
- ربط بالحرفاء والموردين والبضائع

#### 7. فواتير الموردين
- استخراج فواتير تلقائية
- تصفية حسب التاريخ
- طباعة مباشرة

#### 8. الإعدادات
- إعدادات عامة للنظام
- تخصيص خيارات العرض

### 🔄 الربط بين القوائم

النظام مصمم بحيث تكون جميع القوائم مترابطة:

- **تغيير في المشتريات** → يؤثر على الحرفاء، الموردين، البضائع، والداشبورد
- **حذف حريف** → يحذف جميع مشترياته
- **حذف مورد** → يحذف جميع بضائعه ومشترياته
- **تحديث البيانات** → ينعكس فوراً على جميع القوائم ذات الصلة

### 🎨 التصميم

- **الألوان**: أزرق داكن كلون أساسي مع تدرجات أنيقة
- **الخط**: Cairo (يدعم العربية بشكل ممتاز)
- **التخطيط**: قائمة جانبية ثابتة مع محتوى متغير
- **الاستجابة**: يتكيف مع جميع أحجام الشاشات

### 🔮 التطوير المستقبلي

- [ ] تحويل إلى تطبيق Electron
- [ ] إضافة نظام النسخ الاحتياطي
- [ ] تقارير متقدمة وإحصائيات
- [ ] دعم الطباعة المتقدمة
- [ ] نظام المستخدمين والصلاحيات
- [ ] تصدير البيانات لصيغ مختلفة

### 📞 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى إنشاء issue في المستودع أو التواصل مع فريق التطوير.

### 📄 الترخيص

هذا المشروع مرخص تحت [MIT License](LICENSE).

---

**تم تطوير هذا النظام خصيصاً لسوق الجملة للخضر والغلال بجرزونة - نقطة بيع عدد 14 (بيه الغالي)**
