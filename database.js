// نظام قاعدة البيانات المحلية
class LocalDatabase {
    constructor() {
        this.dbName = 'MarketDB';
        this.version = 1;
        this.db = null;
        this.init();
    }

    async init() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.version);
            
            request.onerror = () => reject(request.error);
            request.onsuccess = () => {
                this.db = request.result;
                resolve(this.db);
            };
            
            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                
                // جدول الصناديق
                if (!db.objectStoreNames.contains('boxes')) {
                    const boxStore = db.createObjectStore('boxes', { keyPath: 'id', autoIncrement: true });
                    boxStore.createIndex('name', 'name', { unique: false });
                }
                
                // جدول الحرفاء
                if (!db.objectStoreNames.contains('customers')) {
                    const customerStore = db.createObjectStore('customers', { keyPath: 'id', autoIncrement: true });
                    customerStore.createIndex('name', 'name', { unique: false });
                    customerStore.createIndex('phone', 'phone', { unique: false });
                }
                
                // جدول الموردين
                if (!db.objectStoreNames.contains('suppliers')) {
                    const supplierStore = db.createObjectStore('suppliers', { keyPath: 'id', autoIncrement: true });
                    supplierStore.createIndex('name', 'name', { unique: false });
                }
                
                // جدول البضائع
                if (!db.objectStoreNames.contains('products')) {
                    const productStore = db.createObjectStore('products', { keyPath: 'id', autoIncrement: true });
                    productStore.createIndex('name', 'name', { unique: false });
                    productStore.createIndex('supplier_id', 'supplier_id', { unique: false });
                }
                
                // جدول المشتريات
                if (!db.objectStoreNames.contains('purchases')) {
                    const purchaseStore = db.createObjectStore('purchases', { keyPath: 'id', autoIncrement: true });
                    purchaseStore.createIndex('customer_id', 'customer_id', { unique: false });
                    purchaseStore.createIndex('supplier_id', 'supplier_id', { unique: false });
                    purchaseStore.createIndex('product_id', 'product_id', { unique: false });
                    purchaseStore.createIndex('created_at', 'created_at', { unique: false });
                    purchaseStore.createIndex('payment_status', 'payment_status', { unique: false });
                }
                
                // جدول الدفعات
                if (!db.objectStoreNames.contains('payments')) {
                    const paymentStore = db.createObjectStore('payments', { keyPath: 'id', autoIncrement: true });
                    paymentStore.createIndex('purchase_id', 'purchase_id', { unique: false });
                    paymentStore.createIndex('payment_date', 'payment_date', { unique: false });
                }
                
                // جدول الإعدادات
                if (!db.objectStoreNames.contains('settings')) {
                    const settingStore = db.createObjectStore('settings', { keyPath: 'key' });
                }
            };
        });
    }

    // إضافة سجل جديد
    async add(storeName, data) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            const request = store.add(data);
            
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    // تحديث سجل
    async update(storeName, data) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            const request = store.put(data);
            
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    // حذف سجل
    async delete(storeName, id) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            const request = store.delete(id);
            
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    // جلب سجل واحد
    async get(storeName, id) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const request = store.get(id);
            
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    // جلب جميع السجلات
    async getAll(storeName) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const request = store.getAll();
            
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    // البحث بالفهرس
    async getByIndex(storeName, indexName, value) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const index = store.index(indexName);
            const request = index.getAll(value);
            
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    // البحث بنطاق تاريخي
    async getByDateRange(storeName, startDate, endDate) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const index = store.index('created_at');
            const range = IDBKeyRange.bound(startDate, endDate);
            const request = index.getAll(range);
            
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    // تهيئة البيانات الأولية
    async initializeDefaultData() {
        try {
            // إضافة أنواع الصناديق الافتراضية
            const defaultBoxes = [
                { name: 'صندوق كبير', weight_empty: 2.5, deposit_amount: 10, load_cost: 0.5 },
                { name: 'Plato', weight_empty: 1.8, deposit_amount: 10, load_cost: 0.3 },
                { name: 'صندوق متوسط', weight_empty: 1.5, deposit_amount: 3, load_cost: 0.2 },
                { name: 'صندوق صغير', weight_empty: 1.0, deposit_amount: 3, load_cost: 0.1 },
                { name: 'بلا حمولة', weight_empty: 0, deposit_amount: 0, load_cost: 0.01 }
            ];

            const existingBoxes = await this.getAll('boxes');
            if (existingBoxes.length === 0) {
                for (const box of defaultBoxes) {
                    await this.add('boxes', box);
                }
            }

            // إضافة الإعدادات الافتراضية
            const defaultSettings = [
                { key: 'company_name', value: 'سوق الجملة للخضر والغلال بجرزونة', description: 'اسم الشركة' },
                { key: 'point_name', value: 'نقطة بيع عدد 14 - بيه الغالي', description: 'اسم نقطة البيع' },
                { key: 'currency', value: 'د.ت', description: 'العملة' },
                { key: 'show_notifications', value: 'true', description: 'إظهار التنبيهات' }
            ];

            for (const setting of defaultSettings) {
                try {
                    await this.add('settings', setting);
                } catch (error) {
                    // إذا كان الإعداد موجود، تجاهل الخطأ
                    if (error.name !== 'ConstraintError') {
                        console.error('خطأ في إضافة الإعداد:', error);
                    }
                }
            }

        } catch (error) {
            console.error('خطأ في تهيئة البيانات الافتراضية:', error);
        }
    }
}

// إنشاء مثيل قاعدة البيانات
const db = new LocalDatabase();

// تصدير قاعدة البيانات للاستخدام في ملفات أخرى
window.MarketDB = db;
