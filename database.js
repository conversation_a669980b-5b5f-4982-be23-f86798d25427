// نظام قاعدة البيانات المحلية
class LocalDatabase {
    constructor() {
        this.dbName = 'MarketDB';
        this.version = 1;
        this.db = null;
        this.init();
    }

    async init() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.version);
            
            request.onerror = () => reject(request.error);
            request.onsuccess = () => {
                this.db = request.result;
                resolve(this.db);
            };
            
            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                
                // جدول الصناديق
                if (!db.objectStoreNames.contains('boxes')) {
                    const boxStore = db.createObjectStore('boxes', { keyPath: 'id', autoIncrement: true });
                    boxStore.createIndex('name', 'name', { unique: false });
                }
                
                // جدول الحرفاء
                if (!db.objectStoreNames.contains('customers')) {
                    const customerStore = db.createObjectStore('customers', { keyPath: 'id', autoIncrement: true });
                    customerStore.createIndex('name', 'name', { unique: false });
                    customerStore.createIndex('phone', 'phone', { unique: false });
                }
                
                // جدول الموردين
                if (!db.objectStoreNames.contains('suppliers')) {
                    const supplierStore = db.createObjectStore('suppliers', { keyPath: 'id', autoIncrement: true });
                    supplierStore.createIndex('name', 'name', { unique: false });
                }
                
                // جدول البضائع
                if (!db.objectStoreNames.contains('products')) {
                    const productStore = db.createObjectStore('products', { keyPath: 'id', autoIncrement: true });
                    productStore.createIndex('name', 'name', { unique: false });
                    productStore.createIndex('supplier_id', 'supplier_id', { unique: false });
                }
                
                // جدول المشتريات
                if (!db.objectStoreNames.contains('purchases')) {
                    const purchaseStore = db.createObjectStore('purchases', { keyPath: 'id', autoIncrement: true });
                    purchaseStore.createIndex('customer_id', 'customer_id', { unique: false });
                    purchaseStore.createIndex('supplier_id', 'supplier_id', { unique: false });
                    purchaseStore.createIndex('product_id', 'product_id', { unique: false });
                    purchaseStore.createIndex('created_at', 'created_at', { unique: false });
                    purchaseStore.createIndex('payment_status', 'payment_status', { unique: false });
                }
                
                // جدول الدفعات
                if (!db.objectStoreNames.contains('payments')) {
                    const paymentStore = db.createObjectStore('payments', { keyPath: 'id', autoIncrement: true });
                    paymentStore.createIndex('purchase_id', 'purchase_id', { unique: false });
                    paymentStore.createIndex('payment_date', 'payment_date', { unique: false });
                }
                
                // جدول الإعدادات
                if (!db.objectStoreNames.contains('settings')) {
                    const settingStore = db.createObjectStore('settings', { keyPath: 'key' });
                }
            };
        });
    }

    // إضافة سجل جديد
    async add(storeName, data) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            const request = store.add(data);
            
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    // تحديث سجل
    async update(storeName, data) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            const request = store.put(data);
            
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    // حذف سجل
    async delete(storeName, id) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            const request = store.delete(id);
            
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    // جلب سجل واحد
    async get(storeName, id) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const request = store.get(id);
            
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    // جلب جميع السجلات
    async getAll(storeName) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const request = store.getAll();
            
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    // البحث بالفهرس
    async getByIndex(storeName, indexName, value) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const index = store.index(indexName);
            const request = index.getAll(value);
            
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    // البحث بنطاق تاريخي
    async getByDateRange(storeName, startDate, endDate) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const index = store.index('created_at');
            const range = IDBKeyRange.bound(startDate, endDate);
            const request = index.getAll(range);
            
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    // تهيئة البيانات الأولية
    async initializeDefaultData() {
        try {
            // إضافة أنواع الصناديق الافتراضية
            const defaultBoxes = [
                { name: 'صندوق كبير', weight_empty: 2.5, deposit_amount: 10, load_cost: 0.5 },
                { name: 'Plato', weight_empty: 1.8, deposit_amount: 10, load_cost: 0.3 },
                { name: 'صندوق متوسط', weight_empty: 1.5, deposit_amount: 3, load_cost: 0.2 },
                { name: 'صندوق صغير', weight_empty: 1.0, deposit_amount: 3, load_cost: 0.1 },
                { name: 'بلا حمولة', weight_empty: 0, deposit_amount: 0, load_cost: 0.01 }
            ];

            const existingBoxes = await this.getAll('boxes');
            if (existingBoxes.length === 0) {
                for (const box of defaultBoxes) {
                    await this.add('boxes', box);
                }
            }

            // إضافة الإعدادات الافتراضية
            const defaultSettings = [
                { key: 'company_name', value: 'سوق الجملة للخضر والغلال بجرزونة', description: 'اسم الشركة' },
                { key: 'point_name', value: 'نقطة بيع عدد 14 - بيه الغالي', description: 'اسم نقطة البيع' },
                { key: 'currency', value: 'د.ت', description: 'العملة' },
                { key: 'show_notifications', value: 'true', description: 'إظهار التنبيهات' }
            ];

            for (const setting of defaultSettings) {
                try {
                    await this.add('settings', setting);
                } catch (error) {
                    // إذا كان الإعداد موجود، تجاهل الخطأ
                    if (error.name !== 'ConstraintError') {
                        console.error('خطأ في إضافة الإعداد:', error);
                    }
                }
            }

            // إضافة بيانات تجريبية للحرفاء
            const existingCustomers = await this.getAll('customers');
            if (existingCustomers.length === 0) {
                const defaultCustomers = [
                    { name: 'أحمد محمد', phone: '98123456', address: 'تونس العاصمة', created_at: Date.now() - 86400000 * 30 },
                    { name: 'سارة علي', phone: '97654321', address: 'صفاقس', created_at: Date.now() - 86400000 * 20 },
                    { name: 'محمد صلاح', phone: '96789012', address: 'سوسة', created_at: Date.now() - 86400000 * 15 },
                    { name: 'ليلى حسن', phone: '95432109', address: 'المنستير', created_at: Date.now() - 86400000 * 10 },
                    { name: 'يوسف الطاهر', phone: '94567890', address: 'بنزرت', created_at: Date.now() - 86400000 * 5 }
                ];

                for (const customer of defaultCustomers) {
                    await this.add('customers', customer);
                }
            }

            // إضافة بيانات تجريبية للموردين
            const existingSuppliers = await this.getAll('suppliers');
            if (existingSuppliers.length === 0) {
                const defaultSuppliers = [
                    { name: 'مورد الخضر الطازجة', phone: '71123456', address: 'منطقة الإنتاج الزراعي', created_at: Date.now() - 86400000 * 25 },
                    { name: 'مورد الفواكه المتنوعة', phone: '71234567', address: 'مزارع الشمال', created_at: Date.now() - 86400000 * 20 },
                    { name: 'مورد البطاطس والجزر', phone: '71345678', address: 'مزارع الوسط', created_at: Date.now() - 86400000 * 15 }
                ];

                for (const supplier of defaultSuppliers) {
                    await this.add('suppliers', supplier);
                }
            }

            // إضافة بعض المشتريات التجريبية لإظهار الإحصائيات
            const existingPurchases = await this.getAll('purchases');
            if (existingPurchases.length === 0) {
                const customers = await this.getAll('customers');
                const suppliers = await this.getAll('suppliers');

                if (customers.length > 0 && suppliers.length > 0) {
                    const defaultPurchases = [
                        {
                            customer_id: customers[0].id,
                            supplier_id: suppliers[0].id,
                            product_id: 1,
                            box_id: 1,
                            box_count: 5,
                            gross_weight: 25.5,
                            net_weight: 23.0,
                            price_per_kg: 2.5,
                            total_amount: 57.5,
                            paid_amount: 57.5,
                            payment_status: 'paid',
                            deposit_amount: 50,
                            created_at: Date.now() - 86400000 * 2
                        },
                        {
                            customer_id: customers[1].id,
                            supplier_id: suppliers[1].id,
                            product_id: 2,
                            box_id: 2,
                            box_count: 3,
                            gross_weight: 18.0,
                            net_weight: 16.5,
                            price_per_kg: 3.0,
                            total_amount: 49.5,
                            paid_amount: 25.0,
                            payment_status: 'partial',
                            deposit_amount: 30,
                            created_at: Date.now() - 86400000 * 1
                        },
                        {
                            customer_id: customers[2].id,
                            supplier_id: suppliers[0].id,
                            product_id: 3,
                            box_id: 1,
                            box_count: 8,
                            gross_weight: 42.0,
                            net_weight: 38.0,
                            price_per_kg: 1.8,
                            total_amount: 68.4,
                            paid_amount: 0,
                            payment_status: 'debt',
                            deposit_amount: 80,
                            created_at: Date.now() - 86400000 * 3
                        }
                    ];

                    for (const purchase of defaultPurchases) {
                        await this.add('purchases', purchase);
                    }
                }
            }

        } catch (error) {
            console.error('خطأ في تهيئة البيانات الافتراضية:', error);
        }
    }
}

// إنشاء مثيل قاعدة البيانات
const db = new LocalDatabase();

// تصدير قاعدة البيانات للاستخدام في ملفات أخرى
window.MarketDB = db;
