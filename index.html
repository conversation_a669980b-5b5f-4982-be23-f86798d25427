<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>سوق الجملة للخضر والغلال - نقطة بيع 14</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- القائمة الجانبية -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-store"></i> سوق الجملة</h2>
                <p>نقطة بيع 14 - بيه الغالي</p>
            </div>
            
            <ul class="sidebar-menu">
                <li class="menu-item active" data-page="dashboard">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>لوحة التحكم</span>
                </li>
                <li class="menu-item" data-page="customers">
                    <i class="fas fa-users"></i>
                    <span>الحرفاء</span>
                </li>
                <li class="menu-item" data-page="purchases">
                    <i class="fas fa-shopping-cart"></i>
                    <span>المشتريات</span>
                </li>
                <li class="menu-item" data-page="suppliers">
                    <i class="fas fa-truck"></i>
                    <span>الموردين</span>
                </li>
                <li class="menu-item" data-page="products">
                    <i class="fas fa-apple-alt"></i>
                    <span>البضائع</span>
                </li>
                <li class="menu-item" data-page="boxes">
                    <i class="fas fa-box"></i>
                    <span>الصناديق</span>
                </li>
                <li class="menu-item" data-page="invoices">
                    <i class="fas fa-file-invoice"></i>
                    <span>فواتير الموردين</span>
                </li>
                <li class="menu-item" data-page="settings">
                    <i class="fas fa-cog"></i>
                    <span>الإعدادات</span>
                </li>
            </ul>
        </nav>

        <!-- المحتوى الرئيسي -->
        <main class="main-content">
            <header class="main-header">
                <div class="header-left">
                    <h1 id="page-title">لوحة التحكم</h1>
                </div>
                <div class="header-right">
                    <div class="date-picker-container">
                        <label for="date-filter">التاريخ:</label>
                        <input type="date" id="date-filter" class="date-input">
                    </div>
                    <button class="refresh-btn" onclick="refreshData()">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
            </header>

            <div class="content-area">
                <!-- لوحة التحكم -->
                <div id="dashboard-page" class="page active">
                    <div class="dashboard-cards">
                        <div class="card">
                            <div class="card-icon">
                                <i class="fas fa-money-bill-wave"></i>
                            </div>
                            <div class="card-content">
                                <h3>إجمالي الديون</h3>
                                <p class="card-value" id="total-debts">0.00 د.ت</p>
                            </div>
                        </div>
                        
                        <div class="card">
                            <div class="card-icon">
                                <i class="fas fa-hand-holding-usd"></i>
                            </div>
                            <div class="card-content">
                                <h3>إجمالي الرهن</h3>
                                <p class="card-value" id="total-deposits">0.00 د.ت</p>
                            </div>
                        </div>
                        
                        <div class="card">
                            <div class="card-icon">
                                <i class="fas fa-boxes"></i>
                            </div>
                            <div class="card-content">
                                <h3>عدد الصناديق</h3>
                                <p class="card-value" id="total-boxes">0</p>
                            </div>
                        </div>
                        
                        <div class="card">
                            <div class="card-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="card-content">
                                <h3>مبيعات اليوم</h3>
                                <p class="card-value" id="today-sales">0.00 د.ت</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="dashboard-tables">
                        <div class="table-container">
                            <h3>آخر المشتريات</h3>
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th>الحريف</th>
                                        <th>البضاعة</th>
                                        <th>الكمية</th>
                                        <th>المبلغ</th>
                                        <th>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody id="recent-purchases">
                                    <!-- سيتم ملؤها بـ JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- صفحات أخرى ستضاف هنا -->
                <div id="customers-page" class="page">
                    <div class="page-content">
                        <div class="page-header">
                            <h2>إدارة الحرفاء</h2>
                            <button class="btn btn-primary" onclick="showAddCustomerModal()">
                                <i class="fas fa-plus"></i> إضافة حريف جديد
                            </button>
                        </div>

                        <div class="search-container">
                            <div class="search-box">
                                <i class="fas fa-search"></i>
                                <input type="text" id="customer-search" placeholder="البحث في الحرفاء..." onkeyup="searchCustomers()">
                            </div>
                        </div>

                        <div class="table-container">
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th>الاسم</th>
                                        <th>رقم الهاتف</th>
                                        <th>العنوان</th>
                                        <th>إجمالي المشتريات</th>
                                        <th>المبلغ المدفوع</th>
                                        <th>المتبقي</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="customers-table-body">
                                    <!-- سيتم ملؤها بـ JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div id="purchases-page" class="page">
                    <div class="page-content">
                        <h2>إدارة المشتريات</h2>
                        <!-- محتوى صفحة المشتريات -->
                    </div>
                </div>

                <div id="suppliers-page" class="page">
                    <div class="page-content">
                        <div class="page-header">
                            <h2>إدارة الموردين</h2>
                            <button class="btn btn-primary" onclick="showAddSupplierModal()">
                                <i class="fas fa-plus"></i> إضافة مورد جديد
                            </button>
                        </div>

                        <div class="search-container">
                            <div class="search-box">
                                <i class="fas fa-search"></i>
                                <input type="text" id="supplier-search" placeholder="البحث في الموردين..." onkeyup="searchSuppliers()">
                            </div>
                        </div>

                        <div class="table-container">
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th>اسم المورد</th>
                                        <th>رقم الهاتف</th>
                                        <th>العنوان</th>
                                        <th>عدد البضائع</th>
                                        <th>إجمالي المبيعات</th>
                                        <th>تاريخ التسجيل</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="suppliers-table-body">
                                    <!-- سيتم ملؤها بـ JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div id="products-page" class="page">
                    <div class="page-content">
                        <h2>إدارة البضائع</h2>
                        <!-- محتوى صفحة البضائع -->
                    </div>
                </div>

                <div id="boxes-page" class="page">
                    <div class="page-content">
                        <div class="page-header">
                            <h2>إدارة الصناديق</h2>
                            <button class="btn btn-primary" onclick="showAddBoxModal()">
                                <i class="fas fa-plus"></i> إضافة صندوق جديد
                            </button>
                        </div>

                        <div class="table-container">
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th>اسم الصندوق</th>
                                        <th>الوزن الفارغ (كغ)</th>
                                        <th>مبلغ الرهن (د.ت)</th>
                                        <th>تكلفة الحمولة (د.ت)</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="boxes-table-body">
                                    <!-- سيتم ملؤها بـ JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div id="invoices-page" class="page">
                    <div class="page-content">
                        <h2>فواتير الموردين</h2>
                        <!-- محتوى صفحة الفواتير -->
                    </div>
                </div>

                <div id="settings-page" class="page">
                    <div class="page-content">
                        <h2>الإعدادات</h2>
                        <!-- محتوى صفحة الإعدادات -->
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- نافذة التأكيد -->
    <div id="confirmation-modal" class="modal">
        <div class="modal-content">
            <h3 id="modal-title">تأكيد العملية</h3>
            <p id="modal-message">هل أنت متأكد من هذه العملية؟</p>
            <div class="modal-actions">
                <button class="btn btn-danger" id="confirm-btn">تأكيد</button>
                <button class="btn btn-secondary" id="cancel-btn">إلغاء</button>
            </div>
        </div>
    </div>

    <!-- نافذة التنبيه -->
    <div id="alert-modal" class="modal">
        <div class="modal-content">
            <h3 id="alert-title">تنبيه</h3>
            <p id="alert-message">رسالة التنبيه</p>
            <div class="modal-actions">
                <button class="btn btn-primary" id="alert-ok-btn">موافق</button>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة/تعديل الصندوق -->
    <div id="box-modal" class="modal">
        <div class="modal-content form-modal">
            <h3 id="box-modal-title">إضافة صندوق جديد</h3>
            <form id="box-form">
                <div class="form-group">
                    <label for="box-name">اسم الصندوق:</label>
                    <input type="text" id="box-name" name="name" required>
                </div>

                <div class="form-group">
                    <label for="box-weight">الوزن الفارغ (كغ):</label>
                    <input type="number" id="box-weight" name="weight_empty" step="0.1" min="0" required>
                </div>

                <div class="form-group">
                    <label for="box-deposit">مبلغ الرهن (د.ت):</label>
                    <input type="number" id="box-deposit" name="deposit_amount" step="0.1" min="0" required>
                </div>

                <div class="form-group">
                    <label for="box-load-cost">تكلفة الحمولة (د.ت):</label>
                    <input type="number" id="box-load-cost" name="load_cost" step="0.01" min="0" required>
                </div>

                <div class="modal-actions">
                    <button type="submit" class="btn btn-success">حفظ</button>
                    <button type="button" class="btn btn-secondary" onclick="closeBoxModal()">إلغاء</button>
                </div>
            </form>
        </div>
    </div>

    <!-- نافذة إضافة/تعديل الحريف -->
    <div id="customer-modal" class="modal">
        <div class="modal-content form-modal">
            <h3 id="customer-modal-title">إضافة حريف جديد</h3>
            <form id="customer-form">
                <div class="form-group">
                    <label for="customer-name">اسم الحريف:</label>
                    <input type="text" id="customer-name" name="name" required>
                </div>

                <div class="form-group">
                    <label for="customer-phone">رقم الهاتف:</label>
                    <input type="tel" id="customer-phone" name="phone">
                </div>

                <div class="form-group">
                    <label for="customer-address">العنوان:</label>
                    <textarea id="customer-address" name="address" rows="3"></textarea>
                </div>

                <div class="modal-actions">
                    <button type="submit" class="btn btn-success">حفظ</button>
                    <button type="button" class="btn btn-secondary" onclick="closeCustomerModal()">إلغاء</button>
                </div>
            </form>
        </div>
    </div>

    <!-- نافذة إضافة/تعديل المورد -->
    <div id="supplier-modal" class="modal">
        <div class="modal-content form-modal">
            <h3 id="supplier-modal-title">إضافة مورد جديد</h3>
            <form id="supplier-form">
                <div class="form-group">
                    <label for="supplier-name">اسم المورد:</label>
                    <input type="text" id="supplier-name" name="name" required>
                </div>

                <div class="form-group">
                    <label for="supplier-phone">رقم الهاتف:</label>
                    <input type="tel" id="supplier-phone" name="phone">
                </div>

                <div class="form-group">
                    <label for="supplier-address">العنوان:</label>
                    <textarea id="supplier-address" name="address" rows="3"></textarea>
                </div>

                <div class="modal-actions">
                    <button type="submit" class="btn btn-success">حفظ</button>
                    <button type="button" class="btn btn-secondary" onclick="closeSupplierModal()">إلغاء</button>
                </div>
            </form>
        </div>
    </div>

    <script src="js/database.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
