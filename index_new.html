<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>سوق الجملة للخضر والغلال - بيه الغالي</title>
  <link rel="stylesheet" href="style.css" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
  <div id="app">
    <aside class="sidebar">
      <div class="sidebar-header">
        <h2><i class="fas fa-store"></i> سوق الجملة</h2>
        <p>نقطة بيع 14 - بيه الغالي</p>
      </div>
      <nav>
        <ul>
          <li><button data-page="dashboard" class="active"><i class="fas fa-tachometer-alt"></i> لوحة التحكم</button></li>
          <li><button data-page="customers"><i class="fas fa-users"></i> الحرفاء</button></li>
          <li><button data-page="purchases"><i class="fas fa-shopping-cart"></i> المشتريات</button></li>
          <li><button data-page="suppliers"><i class="fas fa-truck"></i> الموردين</button></li>
          <li><button data-page="goods"><i class="fas fa-apple-alt"></i> البضائع</button></li>
          <li><button data-page="boxes"><i class="fas fa-box"></i> الصناديق</button></li>
          <li><button data-page="invoices"><i class="fas fa-file-invoice"></i> فواتير الموردين</button></li>
          <li><button data-page="settings"><i class="fas fa-cog"></i> الإعدادات</button></li>
        </ul>
      </nav>
    </aside>
    
    <main class="content">
      <header class="main-header">
        <h1 id="page-title">لوحة التحكم</h1>
        <div class="header-controls">
          <input type="date" id="date-filter" class="date-input">
          <button class="refresh-btn" onclick="refreshData()">
            <i class="fas fa-sync-alt"></i>
          </button>
        </div>
      </header>

      <!-- لوحة التحكم -->
      <section id="dashboard" class="page active">
        <div class="cards">
          <div class="card debt-card">
            <div class="card-icon"><i class="fas fa-money-bill-wave"></i></div>
            <div class="card-content">
              <h3>إجمالي الديون</h3>
              <p id="card-debt">0.00 د.ت</p>
            </div>
          </div>
          <div class="card pledge-card">
            <div class="card-icon"><i class="fas fa-hand-holding-usd"></i></div>
            <div class="card-content">
              <h3>إجمالي الرهن</h3>
              <p id="card-pledge">0.00 د.ت</p>
            </div>
          </div>
          <div class="card boxes-card">
            <div class="card-icon"><i class="fas fa-boxes"></i></div>
            <div class="card-content">
              <h3>عدد الصناديق</h3>
              <p id="card-boxes">0</p>
            </div>
          </div>
          <div class="card sales-card">
            <div class="card-icon"><i class="fas fa-chart-line"></i></div>
            <div class="card-content">
              <h3>مبيعات اليوم</h3>
              <p id="card-sales">0.00 د.ت</p>
            </div>
          </div>
        </div>
        
        <div class="recent-purchases">
          <h3>آخر المشتريات</h3>
          <table class="data-table">
            <thead>
              <tr>
                <th>الحريف</th>
                <th>البضاعة</th>
                <th>الكمية</th>
                <th>المبلغ</th>
                <th>الحالة</th>
              </tr>
            </thead>
            <tbody id="recent-purchases-body">
              <!-- سيتم ملؤها بـ JavaScript -->
            </tbody>
          </table>
        </div>
      </section>

      <!-- الحرفاء -->
      <section id="customers" class="page">
        <div class="page-header">
          <h2>إدارة الحرفاء</h2>
          <button class="btn btn-primary" onclick="showAddCustomerModal()">
            <i class="fas fa-plus"></i> إضافة حريف جديد
          </button>
        </div>
        
        <div class="search-container">
          <div class="search-box">
            <i class="fas fa-search"></i>
            <input type="text" id="customer-search" placeholder="البحث في الحرفاء..." onkeyup="searchCustomers()">
          </div>
        </div>
        
        <table class="data-table">
          <thead>
            <tr>
              <th>الاسم</th>
              <th>رقم الهاتف</th>
              <th>العنوان</th>
              <th>إجمالي المشتريات</th>
              <th>المبلغ المدفوع</th>
              <th>المتبقي</th>
              <th>الحالة</th>
              <th>الإجراءات</th>
            </tr>
          </thead>
          <tbody id="customers-table-body">
            <!-- سيتم ملؤها بـ JavaScript -->
          </tbody>
        </table>
      </section>

      <!-- المشتريات -->
      <section id="purchases" class="page">
        <div class="page-header">
          <h2>إدارة المشتريات</h2>
          <button class="btn btn-primary" onclick="showAddPurchaseModal()">
            <i class="fas fa-plus"></i> تسجيل مشترى جديد
          </button>
        </div>
        
        <table class="data-table">
          <thead>
            <tr>
              <th>التاريخ</th>
              <th>الحريف</th>
              <th>المورد</th>
              <th>البضاعة</th>
              <th>نوع الصندوق</th>
              <th>العدد</th>
              <th>الوزن القائم</th>
              <th>الوزن الصافي</th>
              <th>السعر/كغ</th>
              <th>المبلغ الإجمالي</th>
              <th>المدفوع</th>
              <th>الحالة</th>
              <th>الإجراءات</th>
            </tr>
          </thead>
          <tbody id="purchases-table-body">
            <!-- سيتم ملؤها بـ JavaScript -->
          </tbody>
        </table>
      </section>

      <!-- الموردين -->
      <section id="suppliers" class="page">
        <div class="page-header">
          <h2>إدارة الموردين</h2>
          <button class="btn btn-primary" onclick="showAddSupplierModal()">
            <i class="fas fa-plus"></i> إضافة مورد جديد
          </button>
        </div>
        
        <div class="search-container">
          <div class="search-box">
            <i class="fas fa-search"></i>
            <input type="text" id="supplier-search" placeholder="البحث في الموردين..." onkeyup="searchSuppliers()">
          </div>
        </div>
        
        <table class="data-table">
          <thead>
            <tr>
              <th>اسم المورد</th>
              <th>رقم الهاتف</th>
              <th>العنوان</th>
              <th>عدد البضائع</th>
              <th>إجمالي المبيعات</th>
              <th>تاريخ التسجيل</th>
              <th>الإجراءات</th>
            </tr>
          </thead>
          <tbody id="suppliers-table-body">
            <!-- سيتم ملؤها بـ JavaScript -->
          </tbody>
        </table>
      </section>

      <!-- البضائع -->
      <section id="goods" class="page">
        <div class="page-header">
          <h2>إدارة البضائع</h2>
          <button class="btn btn-primary" onclick="showAddProductModal()">
            <i class="fas fa-plus"></i> إضافة بضاعة جديدة
          </button>
        </div>
        
        <table class="data-table">
          <thead>
            <tr>
              <th>اسم البضاعة</th>
              <th>المورد</th>
              <th>الكمية الواردة</th>
              <th>الكمية المباعة</th>
              <th>الكمية المتبقية</th>
              <th>آخر سعر</th>
              <th>الإجراءات</th>
            </tr>
          </thead>
          <tbody id="goods-table-body">
            <!-- سيتم ملؤها بـ JavaScript -->
          </tbody>
        </table>
      </section>

      <!-- الصناديق -->
      <section id="boxes" class="page">
        <div class="page-header">
          <h2>إدارة الصناديق</h2>
          <button class="btn btn-primary" onclick="showAddBoxModal()">
            <i class="fas fa-plus"></i> إضافة صندوق جديد
          </button>
        </div>
        
        <table class="data-table">
          <thead>
            <tr>
              <th>اسم الصندوق</th>
              <th>الوزن الفارغ (كغ)</th>
              <th>مبلغ الرهن (د.ت)</th>
              <th>تكلفة الحمولة (د.ت)</th>
              <th>الإجراءات</th>
            </tr>
          </thead>
          <tbody id="boxes-table-body">
            <!-- سيتم ملؤها بـ JavaScript -->
          </tbody>
        </table>
      </section>

      <!-- فواتير الموردين -->
      <section id="invoices" class="page">
        <div class="page-header">
          <h2>فواتير الموردين</h2>
          <div class="invoice-controls">
            <select id="invoice-supplier">
              <option value="">جميع الموردين</option>
            </select>
            <input type="date" id="invoice-date-from">
            <input type="date" id="invoice-date-to">
            <button class="btn btn-success" onclick="generateInvoices()">
              <i class="fas fa-file-pdf"></i> إنشاء الفواتير
            </button>
          </div>
        </div>
        
        <div id="invoices-container">
          <!-- الفواتير ستظهر هنا -->
        </div>
      </section>

      <!-- الإعدادات -->
      <section id="settings" class="page">
        <div class="page-header">
          <h2>الإعدادات</h2>
        </div>
        
        <div class="settings-container">
          <div class="setting-group">
            <h3>إعدادات عامة</h3>
            <div class="setting-item">
              <label for="company-name">اسم الشركة:</label>
              <input type="text" id="company-name" value="سوق الجملة للخضر والغلال بجرزونة">
            </div>
            <div class="setting-item">
              <label for="point-name">اسم نقطة البيع:</label>
              <input type="text" id="point-name" value="نقطة بيع عدد 14 - بيه الغالي">
            </div>
            <div class="setting-item">
              <label for="currency">العملة:</label>
              <input type="text" id="currency" value="د.ت">
            </div>
            <div class="setting-item">
              <label for="show-notifications">إظهار التنبيهات:</label>
              <input type="checkbox" id="show-notifications" checked>
            </div>
          </div>
          
          <div class="setting-group">
            <h3>إعدادات النسخ الاحتياطي</h3>
            <button class="btn btn-success" onclick="exportData()">
              <i class="fas fa-download"></i> تصدير البيانات
            </button>
            <button class="btn btn-primary" onclick="importData()">
              <i class="fas fa-upload"></i> استيراد البيانات
            </button>
          </div>
        </div>
      </section>
    </main>
  </div>

  <!-- النوافذ المنبثقة -->
  <div id="modal-overlay" class="modal-overlay">
    <div class="modal-content">
      <div class="modal-header">
        <h3 id="modal-title">عنوان النافذة</h3>
        <button class="close-btn" onclick="closeModal()">&times;</button>
      </div>
      <div class="modal-body" id="modal-body">
        <!-- محتوى النافذة -->
      </div>
    </div>
  </div>

  <script src="database.js"></script>
  <script src="utils.js"></script>
  <script src="script.js"></script>
</body>
</html>
