// الملف الرئيسي للتطبيق

// متغيرات عامة
let currentPage = 'dashboard';
let currentDate = getTodayDate();

// تهيئة التطبيق
document.addEventListener('DOMContentLoaded', async function() {
    try {
        // انتظار تهيئة قاعدة البيانات
        await MarketDB.init();
        await MarketDB.initializeDefaultData();
        
        // تهيئة الواجهة
        initializeUI();
        
        // تحميل البيانات الأولية
        await loadDashboardData();
        
        console.log('تم تحميل التطبيق بنجاح');
    } catch (error) {
        console.error('خطأ في تهيئة التطبيق:', error);
        showAlert('خطأ', 'حدث خطأ في تحميل التطبيق');
    }
});

// تهيئة واجهة المستخدم
function initializeUI() {
    // تعيين التاريخ الحالي
    const dateInput = document.getElementById('date-filter');
    if (dateInput) {
        dateInput.value = currentDate;
        dateInput.addEventListener('change', handleDateChange);
    }
    
    // إعداد القائمة الجانبية
    setupSidebarNavigation();
    
    // إعداد أحداث النقر
    setupEventListeners();
}

// إعداد التنقل في القائمة الجانبية
function setupSidebarNavigation() {
    const menuItems = document.querySelectorAll('.menu-item');
    
    menuItems.forEach(item => {
        item.addEventListener('click', function() {
            const page = this.getAttribute('data-page');
            navigateToPage(page);
        });
    });
}

// التنقل بين الصفحات
function navigateToPage(page) {
    // إخفاء جميع الصفحات
    const pages = document.querySelectorAll('.page');
    pages.forEach(p => p.classList.remove('active'));
    
    // إزالة الفئة النشطة من جميع عناصر القائمة
    const menuItems = document.querySelectorAll('.menu-item');
    menuItems.forEach(item => item.classList.remove('active'));
    
    // إظهار الصفحة المطلوبة
    const targetPage = document.getElementById(`${page}-page`);
    if (targetPage) {
        targetPage.classList.add('active');
    }
    
    // تفعيل عنصر القائمة المطلوب
    const targetMenuItem = document.querySelector(`[data-page="${page}"]`);
    if (targetMenuItem) {
        targetMenuItem.classList.add('active');
    }
    
    // تحديث عنوان الصفحة
    updatePageTitle(page);
    
    // تحديث الصفحة الحالية
    currentPage = page;
    
    // تحميل بيانات الصفحة
    loadPageData(page);
}

// تحديث عنوان الصفحة
function updatePageTitle(page) {
    const titles = {
        'dashboard': 'لوحة التحكم',
        'customers': 'إدارة الحرفاء',
        'purchases': 'إدارة المشتريات',
        'suppliers': 'إدارة الموردين',
        'products': 'إدارة البضائع',
        'boxes': 'إدارة الصناديق',
        'invoices': 'فواتير الموردين',
        'settings': 'الإعدادات'
    };
    
    const titleElement = document.getElementById('page-title');
    if (titleElement && titles[page]) {
        titleElement.textContent = titles[page];
    }
}

// تحميل بيانات الصفحة
async function loadPageData(page) {
    try {
        switch (page) {
            case 'dashboard':
                await loadDashboardData();
                break;
            case 'customers':
                await loadCustomersData();
                break;
            case 'purchases':
                await loadPurchasesData();
                break;
            case 'suppliers':
                await loadSuppliersData();
                break;
            case 'products':
                await loadProductsData();
                break;
            case 'boxes':
                await loadBoxesData();
                break;
            case 'invoices':
                await loadInvoicesData();
                break;
            case 'settings':
                await loadSettingsData();
                break;
        }
    } catch (error) {
        console.error(`خطأ في تحميل بيانات صفحة ${page}:`, error);
        showAlert('خطأ', 'حدث خطأ في تحميل البيانات');
    }
}

// تحميل بيانات لوحة التحكم
async function loadDashboardData() {
    try {
        const { start, end } = getDayRange(currentDate);
        
        // جلب المشتريات لليوم المحدد
        const purchases = await MarketDB.getByDateRange('purchases', start, end);
        
        // حساب الإحصائيات
        let totalDebts = 0;
        let totalDeposits = 0;
        let totalBoxes = 0;
        let todaySales = 0;
        
        for (const purchase of purchases) {
            const remaining = purchase.total_amount - purchase.paid_amount;
            if (remaining > 0) {
                totalDebts += remaining;
            }
            
            totalDeposits += purchase.deposit_amount || 0;
            totalBoxes += purchase.box_count || 0;
            todaySales += purchase.total_amount || 0;
        }
        
        // تحديث الكروت
        updateElement('total-debts', formatCurrency(totalDebts));
        updateElement('total-deposits', formatCurrency(totalDeposits));
        updateElement('total-boxes', totalBoxes.toString());
        updateElement('today-sales', formatCurrency(todaySales));
        
        // تحديث جدول آخر المشتريات
        await updateRecentPurchasesTable(purchases.slice(-10));
        
    } catch (error) {
        console.error('خطأ في تحميل بيانات لوحة التحكم:', error);
    }
}

// تحديث جدول آخر المشتريات
async function updateRecentPurchasesTable(purchases) {
    const tbody = document.getElementById('recent-purchases');
    if (!tbody) return;
    
    if (purchases.length === 0) {
        tbody.innerHTML = '<tr><td colspan="5" style="text-align: center;">لا توجد مشتريات</td></tr>';
        return;
    }
    
    let html = '';
    for (const purchase of purchases) {
        try {
            const customer = await MarketDB.get('customers', purchase.customer_id);
            const product = await MarketDB.get('products', purchase.product_id);
            
            const customerName = customer ? customer.name : 'غير محدد';
            const productName = product ? product.name : 'غير محدد';
            const status = formatPaymentStatus(purchase.payment_status);
            const statusClass = getPaymentStatusClass(purchase.payment_status);
            
            html += `
                <tr>
                    <td>${customerName}</td>
                    <td>${productName}</td>
                    <td>${formatWeight(purchase.net_weight)}</td>
                    <td>${formatCurrency(purchase.total_amount)}</td>
                    <td><span class="${statusClass}">${status}</span></td>
                </tr>
            `;
        } catch (error) {
            console.error('خطأ في معالجة المشترى:', error);
        }
    }
    
    tbody.innerHTML = html;
}

// معالجة تغيير التاريخ
function handleDateChange(event) {
    currentDate = event.target.value;
    loadPageData(currentPage);
}

// تحديث البيانات
function refreshData() {
    loadPageData(currentPage);
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // إغلاق النوافذ المنبثقة عند الضغط على Escape
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                modal.style.display = 'none';
            });
        }
    });

    // معالج نموذج الصناديق
    const boxForm = document.getElementById('box-form');
    if (boxForm) {
        boxForm.addEventListener('submit', handleBoxFormSubmit);
    }

    // معالج نموذج الحرفاء
    const customerForm = document.getElementById('customer-form');
    if (customerForm) {
        customerForm.addEventListener('submit', handleCustomerFormSubmit);
    }

    // معالج نموذج الموردين
    const supplierForm = document.getElementById('supplier-form');
    if (supplierForm) {
        supplierForm.addEventListener('submit', handleSupplierFormSubmit);
    }
}

// معالج إرسال نموذج الصندوق
async function handleBoxFormSubmit(event) {
    event.preventDefault();

    try {
        const formData = new FormData(event.target);
        const boxData = {
            name: formData.get('name').trim(),
            weight_empty: parseFloat(formData.get('weight_empty')),
            deposit_amount: parseFloat(formData.get('deposit_amount')),
            load_cost: parseFloat(formData.get('load_cost'))
        };

        // التحقق من صحة البيانات
        validateRequired(boxData.name, 'اسم الصندوق');
        validateNumber(boxData.weight_empty, 'الوزن الفارغ', 0);
        validateNumber(boxData.deposit_amount, 'مبلغ الرهن', 0);
        validateNumber(boxData.load_cost, 'تكلفة الحمولة', 0);

        const boxId = event.target.getAttribute('data-box-id');

        if (boxId) {
            // تعديل صندوق موجود
            boxData.id = parseInt(boxId);
            await MarketDB.update('boxes', boxData);
            showAlert('نجح', 'تم تحديث الصندوق بنجاح');
        } else {
            // إضافة صندوق جديد
            await MarketDB.add('boxes', boxData);
            showAlert('نجح', 'تم إضافة الصندوق بنجاح');
        }

        closeBoxModal();
        await loadBoxesData();

    } catch (error) {
        console.error('خطأ في حفظ الصندوق:', error);
        showAlert('خطأ', error.message || 'حدث خطأ في حفظ الصندوق');
    }
}

// معالج إرسال نموذج الحريف
async function handleCustomerFormSubmit(event) {
    event.preventDefault();

    try {
        const formData = new FormData(event.target);
        const customerData = {
            name: formData.get('name').trim(),
            phone: formData.get('phone').trim(),
            address: formData.get('address').trim(),
            created_at: Date.now()
        };

        // التحقق من صحة البيانات
        validateRequired(customerData.name, 'اسم الحريف');

        const customerId = event.target.getAttribute('data-customer-id');

        if (customerId) {
            // تعديل حريف موجود
            customerData.id = parseInt(customerId);
            // الاحتفاظ بتاريخ الإنشاء الأصلي
            const existingCustomer = await MarketDB.get('customers', parseInt(customerId));
            customerData.created_at = existingCustomer.created_at;

            await MarketDB.update('customers', customerData);
            showAlert('نجح', 'تم تحديث الحريف بنجاح');
        } else {
            // إضافة حريف جديد
            await MarketDB.add('customers', customerData);
            showAlert('نجح', 'تم إضافة الحريف بنجاح');
        }

        closeCustomerModal();
        await loadCustomersData();

    } catch (error) {
        console.error('خطأ في حفظ الحريف:', error);
        showAlert('خطأ', error.message || 'حدث خطأ في حفظ الحريف');
    }
}

// معالج إرسال نموذج المورد
async function handleSupplierFormSubmit(event) {
    event.preventDefault();

    try {
        const formData = new FormData(event.target);
        const supplierData = {
            name: formData.get('name').trim(),
            phone: formData.get('phone').trim(),
            address: formData.get('address').trim(),
            created_at: Date.now()
        };

        // التحقق من صحة البيانات
        validateRequired(supplierData.name, 'اسم المورد');

        const supplierId = event.target.getAttribute('data-supplier-id');

        if (supplierId) {
            // تعديل مورد موجود
            supplierData.id = parseInt(supplierId);
            // الاحتفاظ بتاريخ الإنشاء الأصلي
            const existingSupplier = await MarketDB.get('suppliers', parseInt(supplierId));
            supplierData.created_at = existingSupplier.created_at;

            await MarketDB.update('suppliers', supplierData);
            showAlert('نجح', 'تم تحديث المورد بنجاح');
        } else {
            // إضافة مورد جديد
            await MarketDB.add('suppliers', supplierData);
            showAlert('نجح', 'تم إضافة المورد بنجاح');
        }

        closeSupplierModal();
        await loadSuppliersData();

    } catch (error) {
        console.error('خطأ في حفظ المورد:', error);
        showAlert('خطأ', error.message || 'حدث خطأ في حفظ المورد');
    }
}

// وظائف تحميل البيانات للصفحات الأخرى (ستتم إضافتها لاحقاً)
async function loadCustomersData() {
    try {
        const customers = await MarketDB.getAll('customers');
        const customersWithStats = await calculateCustomersStats(customers);
        updateCustomersTable(customersWithStats);
    } catch (error) {
        console.error('خطأ في تحميل بيانات الحرفاء:', error);
        showAlert('خطأ', 'حدث خطأ في تحميل بيانات الحرفاء');
    }
}

// حساب إحصائيات الحرفاء
async function calculateCustomersStats(customers) {
    const customersWithStats = [];

    for (const customer of customers) {
        try {
            const purchases = await MarketDB.getByIndex('purchases', 'customer_id', customer.id);

            let totalAmount = 0;
            let paidAmount = 0;

            purchases.forEach(purchase => {
                totalAmount += purchase.total_amount || 0;
                paidAmount += purchase.paid_amount || 0;
            });

            const remaining = totalAmount - paidAmount;
            const status = getPaymentStatus(totalAmount, paidAmount);

            customersWithStats.push({
                ...customer,
                totalAmount,
                paidAmount,
                remaining,
                status
            });
        } catch (error) {
            console.error(`خطأ في حساب إحصائيات الحريف ${customer.id}:`, error);
            customersWithStats.push({
                ...customer,
                totalAmount: 0,
                paidAmount: 0,
                remaining: 0,
                status: 'paid'
            });
        }
    }

    return customersWithStats;
}

// تحديث جدول الحرفاء
function updateCustomersTable(customers) {
    const tbody = document.getElementById('customers-table-body');
    if (!tbody) return;

    if (customers.length === 0) {
        tbody.innerHTML = '<tr><td colspan="8" style="text-align: center;">لا توجد حرفاء مسجلين</td></tr>';
        return;
    }

    let html = '';
    customers.forEach(customer => {
        const statusClass = getPaymentStatusClass(customer.status);
        const statusText = formatPaymentStatus(customer.status);

        html += `
            <tr>
                <td>${customer.name}</td>
                <td>${customer.phone || '-'}</td>
                <td>${customer.address || '-'}</td>
                <td>${formatCurrency(customer.totalAmount)}</td>
                <td>${formatCurrency(customer.paidAmount)}</td>
                <td>${formatCurrency(customer.remaining)}</td>
                <td><span class="${statusClass}">${statusText}</span></td>
                <td>
                    <div class="action-buttons">
                        <button class="action-btn edit" onclick="editCustomer(${customer.id})">
                            <i class="fas fa-edit"></i> تعديل
                        </button>
                        <button class="action-btn delete" onclick="deleteCustomer(${customer.id})">
                            <i class="fas fa-trash"></i> حذف
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });

    tbody.innerHTML = html;
}

// البحث في الحرفاء
async function searchCustomers() {
    const searchTerm = document.getElementById('customer-search').value;
    const customers = await MarketDB.getAll('customers');
    const customersWithStats = await calculateCustomersStats(customers);

    const filteredCustomers = smartSearch(customersWithStats, searchTerm, ['name', 'phone', 'address']);
    updateCustomersTable(filteredCustomers);
}

// إظهار نافذة إضافة حريف
function showAddCustomerModal() {
    const modal = document.getElementById('customer-modal');
    const title = document.getElementById('customer-modal-title');
    const form = document.getElementById('customer-form');

    title.textContent = 'إضافة حريف جديد';
    form.reset();
    form.removeAttribute('data-customer-id');
    modal.style.display = 'block';
}

// تعديل حريف
async function editCustomer(customerId) {
    try {
        const customer = await MarketDB.get('customers', customerId);
        if (!customer) {
            showAlert('خطأ', 'لم يتم العثور على الحريف');
            return;
        }

        const modal = document.getElementById('customer-modal');
        const title = document.getElementById('customer-modal-title');
        const form = document.getElementById('customer-form');

        title.textContent = 'تعديل الحريف';
        form.setAttribute('data-customer-id', customerId);

        document.getElementById('customer-name').value = customer.name;
        document.getElementById('customer-phone').value = customer.phone || '';
        document.getElementById('customer-address').value = customer.address || '';

        modal.style.display = 'block';
    } catch (error) {
        console.error('خطأ في تحميل بيانات الحريف:', error);
        showAlert('خطأ', 'حدث خطأ في تحميل بيانات الحريف');
    }
}

// حذف حريف
function deleteCustomer(customerId) {
    showConfirmation(
        'تأكيد الحذف',
        'هل أنت متأكد من حذف هذا الحريف؟ سيتم حذف جميع مشترياته أيضاً. لا يمكن التراجع عن هذه العملية.',
        async () => {
            try {
                // حذف جميع مشتريات الحريف أولاً
                const purchases = await MarketDB.getByIndex('purchases', 'customer_id', customerId);
                for (const purchase of purchases) {
                    await MarketDB.delete('purchases', purchase.id);
                }

                // حذف الحريف
                await MarketDB.delete('customers', customerId);
                await loadCustomersData();
                showAlert('نجح', 'تم حذف الحريف بنجاح');
            } catch (error) {
                console.error('خطأ في حذف الحريف:', error);
                showAlert('خطأ', 'حدث خطأ في حذف الحريف');
            }
        }
    );
}

// إغلاق نافذة الحريف
function closeCustomerModal() {
    const modal = document.getElementById('customer-modal');
    modal.style.display = 'none';
}

async function loadPurchasesData() {
    console.log('تحميل بيانات المشتريات...');
}

async function loadSuppliersData() {
    try {
        const suppliers = await MarketDB.getAll('suppliers');
        const suppliersWithStats = await calculateSuppliersStats(suppliers);
        updateSuppliersTable(suppliersWithStats);
    } catch (error) {
        console.error('خطأ في تحميل بيانات الموردين:', error);
        showAlert('خطأ', 'حدث خطأ في تحميل بيانات الموردين');
    }
}

// حساب إحصائيات الموردين
async function calculateSuppliersStats(suppliers) {
    const suppliersWithStats = [];

    for (const supplier of suppliers) {
        try {
            const products = await MarketDB.getByIndex('products', 'supplier_id', supplier.id);
            const purchases = await MarketDB.getByIndex('purchases', 'supplier_id', supplier.id);

            let totalSales = 0;
            purchases.forEach(purchase => {
                totalSales += purchase.total_amount || 0;
            });

            suppliersWithStats.push({
                ...supplier,
                productCount: products.length,
                totalSales
            });
        } catch (error) {
            console.error(`خطأ في حساب إحصائيات المورد ${supplier.id}:`, error);
            suppliersWithStats.push({
                ...supplier,
                productCount: 0,
                totalSales: 0
            });
        }
    }

    return suppliersWithStats;
}

// تحديث جدول الموردين
function updateSuppliersTable(suppliers) {
    const tbody = document.getElementById('suppliers-table-body');
    if (!tbody) return;

    if (suppliers.length === 0) {
        tbody.innerHTML = '<tr><td colspan="7" style="text-align: center;">لا توجد موردين مسجلين</td></tr>';
        return;
    }

    let html = '';
    suppliers.forEach(supplier => {
        html += `
            <tr>
                <td>${supplier.name}</td>
                <td>${supplier.phone || '-'}</td>
                <td>${supplier.address || '-'}</td>
                <td>${supplier.productCount}</td>
                <td>${formatCurrency(supplier.totalSales)}</td>
                <td>${formatDate(supplier.created_at)}</td>
                <td>
                    <div class="action-buttons">
                        <button class="action-btn edit" onclick="editSupplier(${supplier.id})">
                            <i class="fas fa-edit"></i> تعديل
                        </button>
                        <button class="action-btn delete" onclick="deleteSupplier(${supplier.id})">
                            <i class="fas fa-trash"></i> حذف
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });

    tbody.innerHTML = html;
}

// البحث في الموردين
async function searchSuppliers() {
    const searchTerm = document.getElementById('supplier-search').value;
    const suppliers = await MarketDB.getAll('suppliers');
    const suppliersWithStats = await calculateSuppliersStats(suppliers);

    const filteredSuppliers = smartSearch(suppliersWithStats, searchTerm, ['name', 'phone', 'address']);
    updateSuppliersTable(filteredSuppliers);
}

// إظهار نافذة إضافة مورد
function showAddSupplierModal() {
    const modal = document.getElementById('supplier-modal');
    const title = document.getElementById('supplier-modal-title');
    const form = document.getElementById('supplier-form');

    title.textContent = 'إضافة مورد جديد';
    form.reset();
    form.removeAttribute('data-supplier-id');
    modal.style.display = 'block';
}

// تعديل مورد
async function editSupplier(supplierId) {
    try {
        const supplier = await MarketDB.get('suppliers', supplierId);
        if (!supplier) {
            showAlert('خطأ', 'لم يتم العثور على المورد');
            return;
        }

        const modal = document.getElementById('supplier-modal');
        const title = document.getElementById('supplier-modal-title');
        const form = document.getElementById('supplier-form');

        title.textContent = 'تعديل المورد';
        form.setAttribute('data-supplier-id', supplierId);

        document.getElementById('supplier-name').value = supplier.name;
        document.getElementById('supplier-phone').value = supplier.phone || '';
        document.getElementById('supplier-address').value = supplier.address || '';

        modal.style.display = 'block';
    } catch (error) {
        console.error('خطأ في تحميل بيانات المورد:', error);
        showAlert('خطأ', 'حدث خطأ في تحميل بيانات المورد');
    }
}

// حذف مورد
function deleteSupplier(supplierId) {
    showConfirmation(
        'تأكيد الحذف',
        'هل أنت متأكد من حذف هذا المورد؟ سيتم حذف جميع بضائعه ومشترياته أيضاً. لا يمكن التراجع عن هذه العملية.',
        async () => {
            try {
                // حذف جميع البضائع والمشتريات المرتبطة بالمورد
                const products = await MarketDB.getByIndex('products', 'supplier_id', supplierId);
                for (const product of products) {
                    await MarketDB.delete('products', product.id);
                }

                const purchases = await MarketDB.getByIndex('purchases', 'supplier_id', supplierId);
                for (const purchase of purchases) {
                    await MarketDB.delete('purchases', purchase.id);
                }

                // حذف المورد
                await MarketDB.delete('suppliers', supplierId);
                await loadSuppliersData();
                showAlert('نجح', 'تم حذف المورد بنجاح');
            } catch (error) {
                console.error('خطأ في حذف المورد:', error);
                showAlert('خطأ', 'حدث خطأ في حذف المورد');
            }
        }
    );
}

// إغلاق نافذة المورد
function closeSupplierModal() {
    const modal = document.getElementById('supplier-modal');
    modal.style.display = 'none';
}

async function loadProductsData() {
    console.log('تحميل بيانات البضائع...');
}

async function loadBoxesData() {
    try {
        const boxes = await MarketDB.getAll('boxes');
        updateBoxesTable(boxes);
    } catch (error) {
        console.error('خطأ في تحميل بيانات الصناديق:', error);
        showAlert('خطأ', 'حدث خطأ في تحميل بيانات الصناديق');
    }
}

// تحديث جدول الصناديق
function updateBoxesTable(boxes) {
    const tbody = document.getElementById('boxes-table-body');
    if (!tbody) return;

    if (boxes.length === 0) {
        tbody.innerHTML = '<tr><td colspan="5" style="text-align: center;">لا توجد صناديق مسجلة</td></tr>';
        return;
    }

    let html = '';
    boxes.forEach(box => {
        html += `
            <tr>
                <td>${box.name}</td>
                <td>${formatWeight(box.weight_empty)}</td>
                <td>${formatCurrency(box.deposit_amount)}</td>
                <td>${formatCurrency(box.load_cost)}</td>
                <td>
                    <div class="action-buttons">
                        <button class="action-btn edit" onclick="editBox(${box.id})">
                            <i class="fas fa-edit"></i> تعديل
                        </button>
                        <button class="action-btn delete" onclick="deleteBox(${box.id})">
                            <i class="fas fa-trash"></i> حذف
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });

    tbody.innerHTML = html;
}

// إظهار نافذة إضافة صندوق
function showAddBoxModal() {
    const modal = document.getElementById('box-modal');
    const title = document.getElementById('box-modal-title');
    const form = document.getElementById('box-form');

    title.textContent = 'إضافة صندوق جديد';
    form.reset();
    form.removeAttribute('data-box-id');
    modal.style.display = 'block';
}

// تعديل صندوق
async function editBox(boxId) {
    try {
        const box = await MarketDB.get('boxes', boxId);
        if (!box) {
            showAlert('خطأ', 'لم يتم العثور على الصندوق');
            return;
        }

        const modal = document.getElementById('box-modal');
        const title = document.getElementById('box-modal-title');
        const form = document.getElementById('box-form');

        title.textContent = 'تعديل الصندوق';
        form.setAttribute('data-box-id', boxId);

        document.getElementById('box-name').value = box.name;
        document.getElementById('box-weight').value = box.weight_empty;
        document.getElementById('box-deposit').value = box.deposit_amount;
        document.getElementById('box-load-cost').value = box.load_cost;

        modal.style.display = 'block';
    } catch (error) {
        console.error('خطأ في تحميل بيانات الصندوق:', error);
        showAlert('خطأ', 'حدث خطأ في تحميل بيانات الصندوق');
    }
}

// حذف صندوق
function deleteBox(boxId) {
    showConfirmation(
        'تأكيد الحذف',
        'هل أنت متأكد من حذف هذا الصندوق؟ لا يمكن التراجع عن هذه العملية.',
        async () => {
            try {
                await MarketDB.delete('boxes', boxId);
                await loadBoxesData();
                showAlert('نجح', 'تم حذف الصندوق بنجاح');
            } catch (error) {
                console.error('خطأ في حذف الصندوق:', error);
                showAlert('خطأ', 'حدث خطأ في حذف الصندوق');
            }
        }
    );
}

// إغلاق نافذة الصندوق
function closeBoxModal() {
    const modal = document.getElementById('box-modal');
    modal.style.display = 'none';
}

async function loadInvoicesData() {
    console.log('تحميل بيانات الفواتير...');
}

async function loadSettingsData() {
    console.log('تحميل بيانات الإعدادات...');
}
