// وظائف مساعدة للنظام

// تنسيق التاريخ
function formatDate(date) {
    if (!date) return '';
    const d = new Date(date);
    return d.toLocaleDateString('ar-TN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
    });
}

// تنسيق التاريخ والوقت
function formatDateTime(date) {
    if (!date) return '';
    const d = new Date(date);
    return d.toLocaleString('ar-TN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// تنسيق المبلغ
function formatCurrency(amount) {
    if (amount === null || amount === undefined) return '0.00 د.ت';
    return parseFloat(amount).toFixed(2) + ' د.ت';
}

// تنسيق الوزن
function formatWeight(weight) {
    if (weight === null || weight === undefined) return '0.00 كغ';
    return parseFloat(weight).toFixed(2) + ' كغ';
}

// حساب الوزن الصافي
function calculateNetWeight(grossWeight, boxCount, boxWeight) {
    const gross = parseFloat(grossWeight) || 0;
    const count = parseInt(boxCount) || 0;
    const weight = parseFloat(boxWeight) || 0;
    return Math.max(0, gross - (count * weight));
}

// حساب تكلفة الحمولة
function calculateLoadCost(boxCount, loadCostPerBox) {
    const count = parseInt(boxCount) || 0;
    const cost = parseFloat(loadCostPerBox) || 0;
    return count * cost;
}

// حساب الرهن
function calculateDeposit(boxCount, depositPerBox) {
    const count = parseInt(boxCount) || 0;
    const deposit = parseFloat(depositPerBox) || 0;
    return count * deposit;
}

// حساب المبلغ الإجمالي
function calculateTotalAmount(netWeight, pricePerKg, loadCost) {
    const weight = parseFloat(netWeight) || 0;
    const price = parseFloat(pricePerKg) || 0;
    const load = parseFloat(loadCost) || 0;
    return (weight * price) + load;
}

// تحديد حالة الدفع
function getPaymentStatus(totalAmount, paidAmount) {
    const total = parseFloat(totalAmount) || 0;
    const paid = parseFloat(paidAmount) || 0;
    
    if (paid >= total) return 'paid';
    if (paid > 0) return 'partial';
    return 'debt';
}

// تنسيق حالة الدفع للعرض
function formatPaymentStatus(status) {
    const statusMap = {
        'paid': 'مدفوع',
        'partial': 'تقسيط',
        'debt': 'دين'
    };
    return statusMap[status] || 'غير محدد';
}

// الحصول على فئة CSS لحالة الدفع
function getPaymentStatusClass(status) {
    const classMap = {
        'paid': 'status-paid',
        'partial': 'status-partial',
        'debt': 'status-debt'
    };
    return classMap[status] || '';
}

// التحقق من صحة البيانات
function validateRequired(value, fieldName) {
    if (!value || value.toString().trim() === '') {
        throw new Error(`${fieldName} مطلوب`);
    }
    return true;
}

function validateNumber(value, fieldName, min = null, max = null) {
    const num = parseFloat(value);
    if (isNaN(num)) {
        throw new Error(`${fieldName} يجب أن يكون رقماً صحيحاً`);
    }
    if (min !== null && num < min) {
        throw new Error(`${fieldName} يجب أن يكون أكبر من أو يساوي ${min}`);
    }
    if (max !== null && num > max) {
        throw new Error(`${fieldName} يجب أن يكون أصغر من أو يساوي ${max}`);
    }
    return true;
}

// البحث الذكي
function smartSearch(items, searchTerm, fields) {
    if (!searchTerm || searchTerm.trim() === '') return items;
    
    const term = searchTerm.toLowerCase().trim();
    return items.filter(item => {
        return fields.some(field => {
            const value = item[field];
            if (value === null || value === undefined) return false;
            return value.toString().toLowerCase().includes(term);
        });
    });
}

// إنشاء خيارات للقوائم المنسدلة
function createSelectOptions(items, valueField, textField, placeholder = 'اختر...') {
    let options = `<option value="">${placeholder}</option>`;
    items.forEach(item => {
        options += `<option value="${item[valueField]}">${item[textField]}</option>`;
    });
    return options;
}

// تصدير البيانات إلى CSV
function exportToCSV(data, filename) {
    if (!data || data.length === 0) {
        showAlert('لا توجد بيانات للتصدير');
        return;
    }
    
    const headers = Object.keys(data[0]);
    const csvContent = [
        headers.join(','),
        ...data.map(row => headers.map(header => `"${row[header] || ''}"`).join(','))
    ].join('\n');
    
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// طباعة البيانات
function printData(title, data) {
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
        <head>
            <title>${title}</title>
            <style>
                body { font-family: Arial, sans-serif; direction: rtl; }
                table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                th { background-color: #f2f2f2; }
                h1 { text-align: center; color: #333; }
            </style>
        </head>
        <body>
            <h1>${title}</h1>
            ${data}
        </body>
        </html>
    `);
    printWindow.document.close();
    printWindow.print();
}

// عرض رسالة تأكيد
function showConfirmation(title, message, onConfirm) {
    const modal = document.getElementById('confirmation-modal');
    const titleEl = document.getElementById('modal-title');
    const messageEl = document.getElementById('modal-message');
    const confirmBtn = document.getElementById('confirm-btn');
    const cancelBtn = document.getElementById('cancel-btn');
    
    titleEl.textContent = title;
    messageEl.textContent = message;
    modal.style.display = 'block';
    
    confirmBtn.onclick = () => {
        modal.style.display = 'none';
        if (onConfirm) onConfirm();
    };
    
    cancelBtn.onclick = () => {
        modal.style.display = 'none';
    };
    
    // إغلاق عند النقر خارج النافذة
    modal.onclick = (e) => {
        if (e.target === modal) {
            modal.style.display = 'none';
        }
    };
}

// عرض رسالة تنبيه
function showAlert(title, message = null) {
    const modal = document.getElementById('alert-modal');
    const titleEl = document.getElementById('alert-title');
    const messageEl = document.getElementById('alert-message');
    const okBtn = document.getElementById('alert-ok-btn');
    
    if (message === null) {
        message = title;
        title = 'تنبيه';
    }
    
    titleEl.textContent = title;
    messageEl.textContent = message;
    modal.style.display = 'block';
    
    okBtn.onclick = () => {
        modal.style.display = 'none';
    };
    
    // إغلاق عند النقر خارج النافذة
    modal.onclick = (e) => {
        if (e.target === modal) {
            modal.style.display = 'none';
        }
    };
}

// تحديث عنصر HTML بأمان
function updateElement(elementId, content) {
    const element = document.getElementById(elementId);
    if (element) {
        element.innerHTML = content;
    }
}

// الحصول على تاريخ اليوم
function getTodayDate() {
    return new Date().toISOString().split('T')[0];
}

// الحصول على بداية ونهاية اليوم
function getDayRange(date) {
    const start = new Date(date);
    start.setHours(0, 0, 0, 0);
    
    const end = new Date(date);
    end.setHours(23, 59, 59, 999);
    
    return { start: start.getTime(), end: end.getTime() };
}

// تنظيف النموذج
function clearForm(formId) {
    const form = document.getElementById(formId);
    if (form) {
        form.reset();
    }
}
