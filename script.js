// متغيرات عامة
let currentPage = 'dashboard';
let currentDate = new Date().toISOString().split('T')[0];

// تهيئة التطبيق
document.addEventListener('DOMContentLoaded', async () => {
  try {
    // انتظار تهيئة قاعدة البيانات
    await MarketDB.init();
    await MarketDB.initializeDefaultData();
    
    // تهيئة الواجهة
    initializeUI();
    
    // تحميل البيانات الأولية
    await loadPageData('dashboard');
    
    console.log('تم تحميل التطبيق بنجاح');
  } catch (error) {
    console.error('خطأ في تهيئة التطبيق:', error);
    alert('حدث خطأ في تحميل التطبيق');
  }
});

// تهيئة واجهة المستخدم
function initializeUI() {
  // تعيين التاريخ الحالي
  const dateInput = document.getElementById('date-filter');
  if (dateInput) {
    dateInput.value = currentDate;
    dateInput.addEventListener('change', handleDateChange);
  }
  
  // إعداد التنقل
  setupNavigation();
  
  // إعداد النوافذ المنبثقة
  setupModals();
}

// إعداد التنقل بين الصفحات
function setupNavigation() {
  const buttons = document.querySelectorAll('.sidebar nav ul li button');
  const pages = document.querySelectorAll('.page');

  buttons.forEach(btn => {
    btn.addEventListener('click', () => {
      const pageId = btn.getAttribute('data-page');
      navigateToPage(pageId);
    });
  });
}

// التنقل إلى صفحة معينة
async function navigateToPage(pageId) {
  // إزالة التحديد من جميع الأزرار والصفحات
  const buttons = document.querySelectorAll('.sidebar nav ul li button');
  const pages = document.querySelectorAll('.page');
  
  buttons.forEach(b => b.classList.remove('active'));
  pages.forEach(p => p.classList.remove('active'));

  // تفعيل الزر والصفحة المختارة
  const activeButton = document.querySelector(`[data-page="${pageId}"]`);
  const activePage = document.getElementById(pageId);
  
  if (activeButton) activeButton.classList.add('active');
  if (activePage) activePage.classList.add('active');
  
  // تحديث عنوان الصفحة
  updatePageTitle(pageId);
  
  // تحديث الصفحة الحالية
  currentPage = pageId;
  
  // تحميل بيانات الصفحة
  await loadPageData(pageId);
}

// تحديث عنوان الصفحة
function updatePageTitle(pageId) {
  const titles = {
    'dashboard': 'لوحة التحكم',
    'customers': 'إدارة الحرفاء',
    'purchases': 'إدارة المشتريات',
    'suppliers': 'إدارة الموردين',
    'goods': 'إدارة البضائع',
    'boxes': 'إدارة الصناديق',
    'invoices': 'فواتير الموردين',
    'settings': 'الإعدادات'
  };
  
  const titleElement = document.getElementById('page-title');
  if (titleElement && titles[pageId]) {
    titleElement.textContent = titles[pageId];
  }
}

// تحميل بيانات الصفحة
async function loadPageData(pageId) {
  try {
    switch (pageId) {
      case 'dashboard':
        await loadDashboardData();
        break;
      case 'customers':
        await loadCustomersData();
        break;
      case 'purchases':
        await loadPurchasesData();
        break;
      case 'suppliers':
        await loadSuppliersData();
        break;
      case 'goods':
        await loadGoodsData();
        break;
      case 'boxes':
        await loadBoxesData();
        break;
      case 'invoices':
        await loadInvoicesData();
        break;
      case 'settings':
        await loadSettingsData();
        break;
    }
  } catch (error) {
    console.error(`خطأ في تحميل بيانات صفحة ${pageId}:`, error);
    alert('حدث خطأ في تحميل البيانات');
  }
}

// تحميل بيانات لوحة التحكم
async function loadDashboardData() {
  try {
    const { start, end } = getDayRange(currentDate);
    
    // جلب المشتريات لليوم المحدد
    const purchases = await MarketDB.getByDateRange('purchases', start, end);
    
    // حساب الإحصائيات
    let totalDebts = 0;
    let totalDeposits = 0;
    let totalBoxes = 0;
    let todaySales = 0;
    
    for (const purchase of purchases) {
      const remaining = purchase.total_amount - purchase.paid_amount;
      if (remaining > 0) {
        totalDebts += remaining;
      }
      
      totalDeposits += purchase.deposit_amount || 0;
      totalBoxes += purchase.box_count || 0;
      todaySales += purchase.total_amount || 0;
    }
    
    // تحديث الكروت
    updateElement('card-debt', formatCurrency(totalDebts));
    updateElement('card-pledge', formatCurrency(totalDeposits));
    updateElement('card-boxes', totalBoxes.toString());
    updateElement('card-sales', formatCurrency(todaySales));
    
    // تحديث جدول آخر المشتريات
    await updateRecentPurchasesTable(purchases.slice(-10));
    
  } catch (error) {
    console.error('خطأ في تحميل بيانات لوحة التحكم:', error);
  }
}

// تحديث جدول آخر المشتريات
async function updateRecentPurchasesTable(purchases) {
  const tbody = document.getElementById('recent-purchases-body');
  if (!tbody) return;
  
  if (purchases.length === 0) {
    tbody.innerHTML = '<tr><td colspan="5" style="text-align: center;">لا توجد مشتريات</td></tr>';
    return;
  }
  
  let html = '';
  for (const purchase of purchases) {
    try {
      const customer = await MarketDB.get('customers', purchase.customer_id);
      const product = await MarketDB.get('products', purchase.product_id);
      
      const customerName = customer ? customer.name : 'غير محدد';
      const productName = product ? product.name : 'غير محدد';
      const status = formatPaymentStatus(purchase.payment_status);
      const statusClass = getPaymentStatusClass(purchase.payment_status);
      
      html += `
        <tr>
          <td>${customerName}</td>
          <td>${productName}</td>
          <td>${formatWeight(purchase.net_weight)}</td>
          <td>${formatCurrency(purchase.total_amount)}</td>
          <td><span class="${statusClass}">${status}</span></td>
        </tr>
      `;
    } catch (error) {
      console.error('خطأ في معالجة المشترى:', error);
    }
  }
  
  tbody.innerHTML = html;
}

// تحميل بيانات الحرفاء
async function loadCustomersData() {
  try {
    const customers = await MarketDB.getAll('customers');
    const customersWithStats = await calculateCustomersStats(customers);
    updateCustomersTable(customersWithStats);
  } catch (error) {
    console.error('خطأ في تحميل بيانات الحرفاء:', error);
  }
}

// تحميل بيانات الموردين
async function loadSuppliersData() {
  try {
    const suppliers = await MarketDB.getAll('suppliers');
    const suppliersWithStats = await calculateSuppliersStats(suppliers);
    updateSuppliersTable(suppliersWithStats);
  } catch (error) {
    console.error('خطأ في تحميل بيانات الموردين:', error);
  }
}

// تحميل بيانات الصناديق
async function loadBoxesData() {
  try {
    const boxes = await MarketDB.getAll('boxes');
    updateBoxesTable(boxes);
  } catch (error) {
    console.error('خطأ في تحميل بيانات الصناديق:', error);
  }
}

// وظائف أخرى ستتم إضافتها
async function loadPurchasesData() {
  console.log('تحميل بيانات المشتريات...');
}

async function loadGoodsData() {
  console.log('تحميل بيانات البضائع...');
}

async function loadInvoicesData() {
  console.log('تحميل بيانات الفواتير...');
}

async function loadSettingsData() {
  console.log('تحميل بيانات الإعدادات...');
}

// معالجة تغيير التاريخ
function handleDateChange(event) {
  currentDate = event.target.value;
  loadPageData(currentPage);
}

// تحديث البيانات
function refreshData() {
  loadPageData(currentPage);
}

// إعداد النوافذ المنبثقة
function setupModals() {
  const overlay = document.getElementById('modal-overlay');
  if (overlay) {
    overlay.addEventListener('click', (e) => {
      if (e.target === overlay) {
        closeModal();
      }
    });
  }
  
  // إغلاق النوافذ بمفتاح Escape
  document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
      closeModal();
    }
  });
}

// فتح نافذة منبثقة
function openModal(title, content) {
  const overlay = document.getElementById('modal-overlay');
  const titleElement = document.getElementById('modal-title');
  const bodyElement = document.getElementById('modal-body');
  
  if (overlay && titleElement && bodyElement) {
    titleElement.textContent = title;
    bodyElement.innerHTML = content;
    overlay.classList.add('active');
  }
}

// إغلاق النافذة المنبثقة
function closeModal() {
  const overlay = document.getElementById('modal-overlay');
  if (overlay) {
    overlay.classList.remove('active');
  }
}

// وظائف النوافذ المنبثقة للكيانات المختلفة
function showAddCustomerModal() {
  const content = `
    <form id="customer-form" onsubmit="handleCustomerSubmit(event)">
      <div class="form-group">
        <label for="customer-name">اسم الحريف:</label>
        <input type="text" id="customer-name" name="name" required>
      </div>
      <div class="form-group">
        <label for="customer-phone">رقم الهاتف:</label>
        <input type="tel" id="customer-phone" name="phone">
      </div>
      <div class="form-group">
        <label for="customer-address">العنوان:</label>
        <textarea id="customer-address" name="address" rows="3"></textarea>
      </div>
      <div style="display: flex; gap: 10px; justify-content: flex-end;">
        <button type="submit" class="btn btn-success">حفظ</button>
        <button type="button" class="btn btn-secondary" onclick="closeModal()">إلغاء</button>
      </div>
    </form>
  `;
  openModal('إضافة حريف جديد', content);
}

function showAddSupplierModal() {
  const content = `
    <form id="supplier-form" onsubmit="handleSupplierSubmit(event)">
      <div class="form-group">
        <label for="supplier-name">اسم المورد:</label>
        <input type="text" id="supplier-name" name="name" required>
      </div>
      <div class="form-group">
        <label for="supplier-phone">رقم الهاتف:</label>
        <input type="tel" id="supplier-phone" name="phone">
      </div>
      <div class="form-group">
        <label for="supplier-address">العنوان:</label>
        <textarea id="supplier-address" name="address" rows="3"></textarea>
      </div>
      <div style="display: flex; gap: 10px; justify-content: flex-end;">
        <button type="submit" class="btn btn-success">حفظ</button>
        <button type="button" class="btn btn-secondary" onclick="closeModal()">إلغاء</button>
      </div>
    </form>
  `;
  openModal('إضافة مورد جديد', content);
}

function showAddBoxModal() {
  const content = `
    <form id="box-form" onsubmit="handleBoxSubmit(event)">
      <div class="form-group">
        <label for="box-name">اسم الصندوق:</label>
        <input type="text" id="box-name" name="name" required>
      </div>
      <div class="form-group">
        <label for="box-weight">الوزن الفارغ (كغ):</label>
        <input type="number" id="box-weight" name="weight_empty" step="0.1" min="0" required>
      </div>
      <div class="form-group">
        <label for="box-deposit">مبلغ الرهن (د.ت):</label>
        <input type="number" id="box-deposit" name="deposit_amount" step="0.1" min="0" required>
      </div>
      <div class="form-group">
        <label for="box-load-cost">تكلفة الحمولة (د.ت):</label>
        <input type="number" id="box-load-cost" name="load_cost" step="0.01" min="0" required>
      </div>
      <div style="display: flex; gap: 10px; justify-content: flex-end;">
        <button type="submit" class="btn btn-success">حفظ</button>
        <button type="button" class="btn btn-secondary" onclick="closeModal()">إلغاء</button>
      </div>
    </form>
  `;
  openModal('إضافة صندوق جديد', content);
}

// معالجات النماذج
async function handleCustomerSubmit(event) {
  event.preventDefault();
  try {
    const formData = new FormData(event.target);
    const customerData = {
      name: formData.get('name').trim(),
      phone: formData.get('phone').trim(),
      address: formData.get('address').trim(),
      created_at: Date.now()
    };
    
    await MarketDB.add('customers', customerData);
    closeModal();
    await loadCustomersData();
    alert('تم إضافة الحريف بنجاح');
  } catch (error) {
    console.error('خطأ في حفظ الحريف:', error);
    alert('حدث خطأ في حفظ الحريف');
  }
}

async function handleSupplierSubmit(event) {
  event.preventDefault();
  try {
    const formData = new FormData(event.target);
    const supplierData = {
      name: formData.get('name').trim(),
      phone: formData.get('phone').trim(),
      address: formData.get('address').trim(),
      created_at: Date.now()
    };
    
    await MarketDB.add('suppliers', supplierData);
    closeModal();
    await loadSuppliersData();
    alert('تم إضافة المورد بنجاح');
  } catch (error) {
    console.error('خطأ في حفظ المورد:', error);
    alert('حدث خطأ في حفظ المورد');
  }
}

async function handleBoxSubmit(event) {
  event.preventDefault();
  try {
    const formData = new FormData(event.target);
    const boxData = {
      name: formData.get('name').trim(),
      weight_empty: parseFloat(formData.get('weight_empty')),
      deposit_amount: parseFloat(formData.get('deposit_amount')),
      load_cost: parseFloat(formData.get('load_cost'))
    };
    
    await MarketDB.add('boxes', boxData);
    closeModal();
    await loadBoxesData();
    alert('تم إضافة الصندوق بنجاح');
  } catch (error) {
    console.error('خطأ في حفظ الصندوق:', error);
    alert('حدث خطأ في حفظ الصندوق');
  }
}

// وظائف التعديل والحذف للحرفاء
async function editCustomer(customerId) {
  try {
    const customer = await MarketDB.get('customers', customerId);
    if (!customer) {
      alert('لم يتم العثور على الحريف');
      return;
    }

    const content = `
      <form id="customer-form" onsubmit="handleCustomerUpdate(event, ${customerId})">
        <div class="form-group">
          <label for="customer-name">اسم الحريف:</label>
          <input type="text" id="customer-name" name="name" value="${customer.name}" required>
        </div>
        <div class="form-group">
          <label for="customer-phone">رقم الهاتف:</label>
          <input type="tel" id="customer-phone" name="phone" value="${customer.phone || ''}">
        </div>
        <div class="form-group">
          <label for="customer-address">العنوان:</label>
          <textarea id="customer-address" name="address" rows="3">${customer.address || ''}</textarea>
        </div>
        <div style="display: flex; gap: 10px; justify-content: flex-end;">
          <button type="submit" class="btn btn-success">تحديث</button>
          <button type="button" class="btn btn-secondary" onclick="closeModal()">إلغاء</button>
        </div>
      </form>
    `;
    openModal('تعديل الحريف', content);
  } catch (error) {
    console.error('خطأ في تحميل بيانات الحريف:', error);
    alert('حدث خطأ في تحميل بيانات الحريف');
  }
}

async function handleCustomerUpdate(event, customerId) {
  event.preventDefault();
  try {
    const formData = new FormData(event.target);
    const customerData = {
      id: customerId,
      name: formData.get('name').trim(),
      phone: formData.get('phone').trim(),
      address: formData.get('address').trim(),
      created_at: (await MarketDB.get('customers', customerId)).created_at
    };

    await MarketDB.update('customers', customerData);
    closeModal();
    await loadCustomersData();
    alert('تم تحديث الحريف بنجاح');
  } catch (error) {
    console.error('خطأ في تحديث الحريف:', error);
    alert('حدث خطأ في تحديث الحريف');
  }
}

async function deleteCustomer(customerId) {
  if (confirm('هل أنت متأكد من حذف هذا الحريف؟ سيتم حذف جميع مشترياته أيضاً.')) {
    try {
      // حذف جميع مشتريات الحريف أولاً
      const purchases = await MarketDB.getByIndex('purchases', 'customer_id', customerId);
      for (const purchase of purchases) {
        await MarketDB.delete('purchases', purchase.id);
      }

      // حذف الحريف
      await MarketDB.delete('customers', customerId);
      await loadCustomersData();
      alert('تم حذف الحريف بنجاح');
    } catch (error) {
      console.error('خطأ في حذف الحريف:', error);
      alert('حدث خطأ في حذف الحريف');
    }
  }
}

// وظائف التعديل والحذف للموردين
async function editSupplier(supplierId) {
  try {
    const supplier = await MarketDB.get('suppliers', supplierId);
    if (!supplier) {
      alert('لم يتم العثور على المورد');
      return;
    }

    const content = `
      <form id="supplier-form" onsubmit="handleSupplierUpdate(event, ${supplierId})">
        <div class="form-group">
          <label for="supplier-name">اسم المورد:</label>
          <input type="text" id="supplier-name" name="name" value="${supplier.name}" required>
        </div>
        <div class="form-group">
          <label for="supplier-phone">رقم الهاتف:</label>
          <input type="tel" id="supplier-phone" name="phone" value="${supplier.phone || ''}">
        </div>
        <div class="form-group">
          <label for="supplier-address">العنوان:</label>
          <textarea id="supplier-address" name="address" rows="3">${supplier.address || ''}</textarea>
        </div>
        <div style="display: flex; gap: 10px; justify-content: flex-end;">
          <button type="submit" class="btn btn-success">تحديث</button>
          <button type="button" class="btn btn-secondary" onclick="closeModal()">إلغاء</button>
        </div>
      </form>
    `;
    openModal('تعديل المورد', content);
  } catch (error) {
    console.error('خطأ في تحميل بيانات المورد:', error);
    alert('حدث خطأ في تحميل بيانات المورد');
  }
}

async function handleSupplierUpdate(event, supplierId) {
  event.preventDefault();
  try {
    const formData = new FormData(event.target);
    const supplierData = {
      id: supplierId,
      name: formData.get('name').trim(),
      phone: formData.get('phone').trim(),
      address: formData.get('address').trim(),
      created_at: (await MarketDB.get('suppliers', supplierId)).created_at
    };

    await MarketDB.update('suppliers', supplierData);
    closeModal();
    await loadSuppliersData();
    alert('تم تحديث المورد بنجاح');
  } catch (error) {
    console.error('خطأ في تحديث المورد:', error);
    alert('حدث خطأ في تحديث المورد');
  }
}

async function deleteSupplier(supplierId) {
  if (confirm('هل أنت متأكد من حذف هذا المورد؟ سيتم حذف جميع بضائعه ومشترياته أيضاً.')) {
    try {
      // حذف جميع البضائع والمشتريات المرتبطة بالمورد
      const products = await MarketDB.getByIndex('products', 'supplier_id', supplierId);
      for (const product of products) {
        await MarketDB.delete('products', product.id);
      }

      const purchases = await MarketDB.getByIndex('purchases', 'supplier_id', supplierId);
      for (const purchase of purchases) {
        await MarketDB.delete('purchases', purchase.id);
      }

      // حذف المورد
      await MarketDB.delete('suppliers', supplierId);
      await loadSuppliersData();
      alert('تم حذف المورد بنجاح');
    } catch (error) {
      console.error('خطأ في حذف المورد:', error);
      alert('حدث خطأ في حذف المورد');
    }
  }
}

// وظائف التعديل والحذف للصناديق
async function editBox(boxId) {
  try {
    const box = await MarketDB.get('boxes', boxId);
    if (!box) {
      alert('لم يتم العثور على الصندوق');
      return;
    }

    const content = `
      <form id="box-form" onsubmit="handleBoxUpdate(event, ${boxId})">
        <div class="form-group">
          <label for="box-name">اسم الصندوق:</label>
          <input type="text" id="box-name" name="name" value="${box.name}" required>
        </div>
        <div class="form-group">
          <label for="box-weight">الوزن الفارغ (كغ):</label>
          <input type="number" id="box-weight" name="weight_empty" step="0.1" min="0" value="${box.weight_empty}" required>
        </div>
        <div class="form-group">
          <label for="box-deposit">مبلغ الرهن (د.ت):</label>
          <input type="number" id="box-deposit" name="deposit_amount" step="0.1" min="0" value="${box.deposit_amount}" required>
        </div>
        <div class="form-group">
          <label for="box-load-cost">تكلفة الحمولة (د.ت):</label>
          <input type="number" id="box-load-cost" name="load_cost" step="0.01" min="0" value="${box.load_cost}" required>
        </div>
        <div style="display: flex; gap: 10px; justify-content: flex-end;">
          <button type="submit" class="btn btn-success">تحديث</button>
          <button type="button" class="btn btn-secondary" onclick="closeModal()">إلغاء</button>
        </div>
      </form>
    `;
    openModal('تعديل الصندوق', content);
  } catch (error) {
    console.error('خطأ في تحميل بيانات الصندوق:', error);
    alert('حدث خطأ في تحميل بيانات الصندوق');
  }
}

async function handleBoxUpdate(event, boxId) {
  event.preventDefault();
  try {
    const formData = new FormData(event.target);
    const boxData = {
      id: boxId,
      name: formData.get('name').trim(),
      weight_empty: parseFloat(formData.get('weight_empty')),
      deposit_amount: parseFloat(formData.get('deposit_amount')),
      load_cost: parseFloat(formData.get('load_cost'))
    };

    await MarketDB.update('boxes', boxData);
    closeModal();
    await loadBoxesData();
    alert('تم تحديث الصندوق بنجاح');
  } catch (error) {
    console.error('خطأ في تحديث الصندوق:', error);
    alert('حدث خطأ في تحديث الصندوق');
  }
}

async function deleteBox(boxId) {
  if (confirm('هل أنت متأكد من حذف هذا الصندوق؟')) {
    try {
      await MarketDB.delete('boxes', boxId);
      await loadBoxesData();
      alert('تم حذف الصندوق بنجاح');
    } catch (error) {
      console.error('خطأ في حذف الصندوق:', error);
      alert('حدث خطأ في حذف الصندوق');
    }
  }
}
