// متغيرات عامة
let currentPage = 'dashboard';
let currentDate = new Date().toISOString().split('T')[0];
let selectedCustomerId = null;

// بيانات الحرفاء التجريبية الجديدة
let customers = [
  {
    id: 1,
    name: "أحمد محمد",
    goods: "بطاطا",
    boxCount: 5,
    boxType: "صندوق كبير",
    deposit: 50,
    paidAmount: 200,
    totalAmount: 200 // تُحسب الحالة منه
  },
  {
    id: 2,
    name: "سارة علي",
    goods: "طماطم",
    boxCount: 3,
    boxType: "Plato",
    deposit: 30,
    paidAmount: 100,
    totalAmount: 300
  },
  {
    id: 3,
    name: "محمد صلاح",
    goods: "بصل",
    boxCount: 2,
    boxType: "Scarface",
    deposit: 6,
    paidAmount: 0,
    totalAmount: 120
  },
  {
    id: 4,
    name: "ليلى حسن",
    goods: "جزر",
    boxCount: 4,
    boxType: "صندوق متوسط",
    deposit: 12,
    paidAmount: 180,
    totalAmount: 180
  },
  {
    id: 5,
    name: "يوسف الطاهر",
    goods: "فلفل",
    boxCount: 6,
    boxType: "صندوق صغير",
    deposit: 18,
    paidAmount: 50,
    totalAmount: 150
  }
];

// دالة حساب حالة الدفع تلقائياً
function getPaymentStatus(customer) {
  if (customer.paidAmount >= customer.totalAmount) return "مدفوع";
  if (customer.paidAmount > 0) return "بالتقسيط";
  return "دين";
}

// دالة للحصول على فئة CSS لحالة الدفع
function getPaymentStatusClass(status) {
  switch(status) {
    case "مدفوع": return "status-paid-table";
    case "بالتقسيط": return "status-partial-table";
    case "دين": return "status-debt-table";
    default: return "status-paid-table";
  }
}

// تهيئة التطبيق
document.addEventListener('DOMContentLoaded', async () => {
  try {
    // انتظار تهيئة قاعدة البيانات
    await MarketDB.init();
    await MarketDB.initializeDefaultData();
    
    // تهيئة الواجهة
    initializeUI();
    
    // تحميل البيانات الأولية
    await loadPageData('dashboard');

    // عرض البيانات التجريبية للحرفاء عند تحميل الصفحة
    renderCustomers(customers);

    console.log('تم تحميل التطبيق بنجاح');
  } catch (error) {
    console.error('خطأ في تهيئة التطبيق:', error);
    alert('حدث خطأ في تحميل التطبيق');
  }
});

// تهيئة واجهة المستخدم
function initializeUI() {
  // تعيين التاريخ الحالي
  const dateInput = document.getElementById('date-filter');
  if (dateInput) {
    dateInput.value = currentDate;
    dateInput.addEventListener('change', handleDateChange);
  }
  
  // إعداد التنقل
  setupNavigation();
  
  // إعداد النوافذ المنبثقة
  setupModals();

  // إعداد البحث في الحرفاء
  setupCustomerSearch();

  // إعداد نافذة التعديل
  setupEditModal();
}

// إعداد التنقل بين الصفحات
function setupNavigation() {
  const buttons = document.querySelectorAll('.sidebar nav ul li button');
  const pages = document.querySelectorAll('.page');

  buttons.forEach(btn => {
    btn.addEventListener('click', () => {
      const pageId = btn.getAttribute('data-page');
      navigateToPage(pageId);
    });
  });
}

// التنقل إلى صفحة معينة
async function navigateToPage(pageId) {
  // إزالة التحديد من جميع الأزرار والصفحات
  const buttons = document.querySelectorAll('.sidebar nav ul li button');
  const pages = document.querySelectorAll('.page');
  
  buttons.forEach(b => b.classList.remove('active'));
  pages.forEach(p => p.classList.remove('active'));

  // تفعيل الزر والصفحة المختارة
  const activeButton = document.querySelector(`[data-page="${pageId}"]`);
  const activePage = document.getElementById(pageId);
  
  if (activeButton) activeButton.classList.add('active');
  if (activePage) activePage.classList.add('active');
  
  // تحديث عنوان الصفحة
  updatePageTitle(pageId);
  
  // تحديث الصفحة الحالية
  currentPage = pageId;
  
  // تحميل بيانات الصفحة
  await loadPageData(pageId);
}

// تحديث عنوان الصفحة
function updatePageTitle(pageId) {
  const titles = {
    'dashboard': 'لوحة التحكم',
    'customers': 'إدارة الحرفاء',
    'purchases': 'إدارة المشتريات',
    'suppliers': 'إدارة الموردين',
    'goods': 'إدارة البضائع',
    'boxes': 'إدارة الصناديق',
    'invoices': 'فواتير الموردين',
    'settings': 'الإعدادات'
  };
  
  const titleElement = document.getElementById('page-title');
  if (titleElement && titles[pageId]) {
    titleElement.textContent = titles[pageId];
  }
}

// تحميل بيانات الصفحة
async function loadPageData(pageId) {
  try {
    switch (pageId) {
      case 'dashboard':
        await loadDashboardData();
        break;
      case 'customers':
        await loadCustomersData();
        break;
      case 'purchases':
        await loadPurchasesData();
        break;
      case 'suppliers':
        await loadSuppliersData();
        break;
      case 'goods':
        await loadGoodsData();
        break;
      case 'boxes':
        await loadBoxesData();
        break;
      case 'invoices':
        await loadInvoicesData();
        break;
      case 'settings':
        await loadSettingsData();
        break;
    }
  } catch (error) {
    console.error(`خطأ في تحميل بيانات صفحة ${pageId}:`, error);
    alert('حدث خطأ في تحميل البيانات');
  }
}

// تحميل بيانات لوحة التحكم
async function loadDashboardData() {
  try {
    const { start, end } = getDayRange(currentDate);
    
    // جلب المشتريات لليوم المحدد
    const purchases = await MarketDB.getByDateRange('purchases', start, end);
    
    // حساب الإحصائيات
    let totalDebts = 0;
    let totalDeposits = 0;
    let totalBoxes = 0;
    let todaySales = 0;
    
    for (const purchase of purchases) {
      const remaining = purchase.total_amount - purchase.paid_amount;
      if (remaining > 0) {
        totalDebts += remaining;
      }
      
      totalDeposits += purchase.deposit_amount || 0;
      totalBoxes += purchase.box_count || 0;
      todaySales += purchase.total_amount || 0;
    }
    
    // تحديث الكروت
    updateElement('card-debt', formatCurrency(totalDebts));
    updateElement('card-pledge', formatCurrency(totalDeposits));
    updateElement('card-boxes', totalBoxes.toString());
    updateElement('card-sales', formatCurrency(todaySales));
    
    // تحديث جدول آخر المشتريات
    await updateRecentPurchasesTable(purchases.slice(-10));
    
  } catch (error) {
    console.error('خطأ في تحميل بيانات لوحة التحكم:', error);
  }
}

// تحديث جدول آخر المشتريات
async function updateRecentPurchasesTable(purchases) {
  const tbody = document.getElementById('recent-purchases-body');
  if (!tbody) return;
  
  if (purchases.length === 0) {
    tbody.innerHTML = '<tr><td colspan="5" style="text-align: center;">لا توجد مشتريات</td></tr>';
    return;
  }
  
  let html = '';
  for (const purchase of purchases) {
    try {
      const customer = await MarketDB.get('customers', purchase.customer_id);
      const product = await MarketDB.get('products', purchase.product_id);
      
      const customerName = customer ? customer.name : 'غير محدد';
      const productName = product ? product.name : 'غير محدد';
      const status = formatPaymentStatus(purchase.payment_status);
      const statusClass = getPaymentStatusClass(purchase.payment_status);
      
      html += `
        <tr>
          <td>${customerName}</td>
          <td>${productName}</td>
          <td>${formatWeight(purchase.net_weight)}</td>
          <td>${formatCurrency(purchase.total_amount)}</td>
          <td><span class="${statusClass}">${status}</span></td>
        </tr>
      `;
    } catch (error) {
      console.error('خطأ في معالجة المشترى:', error);
    }
  }
  
  tbody.innerHTML = html;
}

// تحميل بيانات الحرفاء
async function loadCustomersData() {
  try {
    // استخدام البيانات التجريبية الجديدة
    renderCustomers(customers);
  } catch (error) {
    console.error('خطأ في تحميل بيانات الحرفاء:', error);
  }
}

// دالة لعرض الحرفاء في الجدول الجديد
function renderCustomers(customersToShow) {
  const tbody = document.querySelector("#customersTable tbody");
  if (!tbody) return;

  tbody.innerHTML = "";

  if (customersToShow.length === 0) {
    tbody.innerHTML = '<tr><td colspan="9" style="text-align: center; padding: 20px;">لا توجد حرفاء مسجلين</td></tr>';
    return;
  }

  customersToShow.forEach(customer => {
    const status = getPaymentStatus(customer);
    const statusClass = getPaymentStatusClass(status);

    const tr = document.createElement("tr");
    tr.innerHTML = `
      <td>${customer.id}</td>
      <td>${customer.name}</td>
      <td>${customer.goods}</td>
      <td>${customer.boxCount}</td>
      <td>${customer.boxType}</td>
      <td>${customer.deposit.toFixed(2)}</td>
      <td>${customer.paidAmount.toFixed(2)}</td>
      <td><span class="${statusClass}">${status}</span></td>
      <td>
        <button class="action-btn edit" data-id="${customer.id}">
          <i class="fas fa-edit"></i> تعديل
        </button>
        <button class="action-btn delete" data-id="${customer.id}">
          <i class="fas fa-trash"></i> حذف
        </button>
        <button class="action-btn report" data-id="${customer.id}">
          <i class="fas fa-file-alt"></i> تقرير
        </button>
      </td>
    `;

    tbody.appendChild(tr);
  });

  addCustomerTableListeners();
}

// إضافة الاستماع لأزرار الإجراءات في جدول الحرفاء
function addCustomerTableListeners() {
  document.querySelectorAll(".action-btn.edit").forEach(btn => {
    btn.addEventListener("click", () => {
      const id = parseInt(btn.dataset.id);
      selectedCustomerId = id;
      const customer = customers.find(c => c.id === id);

      // تحديث عنوان النافذة
      document.getElementById("editModalTitle").textContent = `تعديل معطيات الحريف: ${customer.name}`;

      // ملء الحقول
      document.getElementById("editBoxCount").value = customer.boxCount;
      document.getElementById("editBoxType").value = customer.boxType;
      document.getElementById("editPaidAmount").value = 0; // صفر بشكل افتراضي
      document.getElementById("editDeposit").value = customer.deposit;

      document.getElementById("editModal").classList.remove("hidden");
    });
  });

  document.querySelectorAll(".action-btn.delete").forEach(btn => {
    btn.onclick = () => {
      const id = parseInt(btn.dataset.id);
      deleteCustomerFromTable(id);
    };
  });

  document.querySelectorAll(".action-btn.report").forEach(btn => {
    btn.onclick = () => {
      const id = parseInt(btn.dataset.id);
      reportCustomer(id);
    };
  });
}

// تحميل بيانات الموردين
async function loadSuppliersData() {
  try {
    const suppliers = await MarketDB.getAll('suppliers');
    const suppliersWithStats = await calculateSuppliersStats(suppliers);
    updateSuppliersTable(suppliersWithStats);
  } catch (error) {
    console.error('خطأ في تحميل بيانات الموردين:', error);
  }
}

// تحميل بيانات الصناديق
async function loadBoxesData() {
  try {
    const boxes = await MarketDB.getAll('boxes');
    updateBoxesTable(boxes);
  } catch (error) {
    console.error('خطأ في تحميل بيانات الصناديق:', error);
  }
}

// وظائف أخرى ستتم إضافتها
async function loadPurchasesData() {
  console.log('تحميل بيانات المشتريات...');
}

async function loadGoodsData() {
  console.log('تحميل بيانات البضائع...');
}

async function loadInvoicesData() {
  console.log('تحميل بيانات الفواتير...');
}

async function loadSettingsData() {
  console.log('تحميل بيانات الإعدادات...');
}

// معالجة تغيير التاريخ
function handleDateChange(event) {
  currentDate = event.target.value;
  loadPageData(currentPage);
}

// تحديث البيانات
function refreshData() {
  loadPageData(currentPage);
}

// إعداد النوافذ المنبثقة
function setupModals() {
  const overlay = document.getElementById('modal-overlay');
  if (overlay) {
    overlay.addEventListener('click', (e) => {
      if (e.target === overlay) {
        closeModal();
      }
    });
  }
  
  // إغلاق النوافذ بمفتاح Escape
  document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
      closeModal();
    }
  });
}

// فتح نافذة منبثقة
function openModal(title, content) {
  const overlay = document.getElementById('modal-overlay');
  const titleElement = document.getElementById('modal-title');
  const bodyElement = document.getElementById('modal-body');
  
  if (overlay && titleElement && bodyElement) {
    titleElement.textContent = title;
    bodyElement.innerHTML = content;
    overlay.classList.add('active');
  }
}

// إغلاق النافذة المنبثقة
function closeModal() {
  const overlay = document.getElementById('modal-overlay');
  if (overlay) {
    overlay.classList.remove('active');
  }
}

// وظائف النوافذ المنبثقة للكيانات المختلفة
function showAddCustomerModal() {
  const content = `
    <form id="customer-form" onsubmit="handleCustomerSubmit(event)">
      <div class="form-group">
        <label for="customer-name">اسم الحريف:</label>
        <input type="text" id="customer-name" name="name" required>
      </div>
      <div class="form-group">
        <label for="customer-goods">نوع البضاعة:</label>
        <input type="text" id="customer-goods" name="goods" required>
      </div>
      <div class="form-group">
        <label for="customer-boxCount">عدد الصناديق:</label>
        <input type="number" id="customer-boxCount" name="boxCount" min="1" required>
      </div>
      <div class="form-group">
        <label for="customer-boxType">نوع الصناديق:</label>
        <select id="customer-boxType" name="boxType" required>
          <option value="">اختر نوع الصندوق</option>
          <option value="صندوق كبير">صندوق كبير</option>
          <option value="Plato">Plato</option>
          <option value="Scarface">Scarface</option>
          <option value="صندوق متوسط">صندوق متوسط</option>
          <option value="صندوق صغير">صندوق صغير</option>
        </select>
      </div>
      <div class="form-group">
        <label for="customer-deposit">الرهن (د.ت):</label>
        <input type="number" id="customer-deposit" name="deposit" step="0.01" min="0" required>
      </div>
      <div class="form-group">
        <label for="customer-totalAmount">المبلغ الإجمالي (د.ت):</label>
        <input type="number" id="customer-totalAmount" name="totalAmount" step="0.01" min="0" required>
      </div>
      <div class="form-group">
        <label for="customer-paidAmount">المبلغ المدفوع (د.ت):</label>
        <input type="number" id="customer-paidAmount" name="paidAmount" step="0.01" min="0" value="0" required>
      </div>
      <div style="display: flex; gap: 10px; justify-content: flex-end;">
        <button type="submit" class="btn btn-success">حفظ</button>
        <button type="button" class="btn btn-secondary" onclick="closeModal()">إلغاء</button>
      </div>
    </form>
  `;
  openModal('إضافة حريف جديد', content);
}

function showAddSupplierModal() {
  const content = `
    <form id="supplier-form" onsubmit="handleSupplierSubmit(event)">
      <div class="form-group">
        <label for="supplier-name">اسم المورد:</label>
        <input type="text" id="supplier-name" name="name" required>
      </div>
      <div class="form-group">
        <label for="supplier-phone">رقم الهاتف:</label>
        <input type="tel" id="supplier-phone" name="phone">
      </div>
      <div class="form-group">
        <label for="supplier-address">العنوان:</label>
        <textarea id="supplier-address" name="address" rows="3"></textarea>
      </div>
      <div style="display: flex; gap: 10px; justify-content: flex-end;">
        <button type="submit" class="btn btn-success">حفظ</button>
        <button type="button" class="btn btn-secondary" onclick="closeModal()">إلغاء</button>
      </div>
    </form>
  `;
  openModal('إضافة مورد جديد', content);
}

function showAddBoxModal() {
  const content = `
    <form id="box-form" onsubmit="handleBoxSubmit(event)">
      <div class="form-group">
        <label for="box-name">اسم الصندوق:</label>
        <input type="text" id="box-name" name="name" required>
      </div>
      <div class="form-group">
        <label for="box-weight">الوزن الفارغ (كغ):</label>
        <input type="number" id="box-weight" name="weight_empty" step="0.1" min="0" required>
      </div>
      <div class="form-group">
        <label for="box-deposit">مبلغ الرهن (د.ت):</label>
        <input type="number" id="box-deposit" name="deposit_amount" step="0.1" min="0" required>
      </div>
      <div class="form-group">
        <label for="box-load-cost">تكلفة الحمولة (د.ت):</label>
        <input type="number" id="box-load-cost" name="load_cost" step="0.01" min="0" required>
      </div>
      <div style="display: flex; gap: 10px; justify-content: flex-end;">
        <button type="submit" class="btn btn-success">حفظ</button>
        <button type="button" class="btn btn-secondary" onclick="closeModal()">إلغاء</button>
      </div>
    </form>
  `;
  openModal('إضافة صندوق جديد', content);
}

// معالجات النماذج
function handleCustomerSubmit(event) {
  event.preventDefault();
  const formData = new FormData(event.target);

  // إنشاء ID جديد
  const newId = Math.max(...customers.map(c => c.id), 0) + 1;

  const newCustomer = {
    id: newId,
    name: formData.get('name').trim(),
    goods: formData.get('goods').trim(),
    boxCount: parseInt(formData.get('boxCount')),
    boxType: formData.get('boxType'),
    deposit: parseFloat(formData.get('deposit')),
    totalAmount: parseFloat(formData.get('totalAmount')),
    paidAmount: parseFloat(formData.get('paidAmount'))
  };

  customers.push(newCustomer);
  closeModal();
  renderCustomers(customers);
  alert('تم إضافة الحريف بنجاح');
}

async function handleSupplierSubmit(event) {
  event.preventDefault();
  try {
    const formData = new FormData(event.target);
    const supplierData = {
      name: formData.get('name').trim(),
      phone: formData.get('phone').trim(),
      address: formData.get('address').trim(),
      created_at: Date.now()
    };
    
    await MarketDB.add('suppliers', supplierData);
    closeModal();
    await loadSuppliersData();
    alert('تم إضافة المورد بنجاح');
  } catch (error) {
    console.error('خطأ في حفظ المورد:', error);
    alert('حدث خطأ في حفظ المورد');
  }
}

async function handleBoxSubmit(event) {
  event.preventDefault();
  try {
    const formData = new FormData(event.target);
    const boxData = {
      name: formData.get('name').trim(),
      weight_empty: parseFloat(formData.get('weight_empty')),
      deposit_amount: parseFloat(formData.get('deposit_amount')),
      load_cost: parseFloat(formData.get('load_cost'))
    };
    
    await MarketDB.add('boxes', boxData);
    closeModal();
    await loadBoxesData();
    alert('تم إضافة الصندوق بنجاح');
  } catch (error) {
    console.error('خطأ في حفظ الصندوق:', error);
    alert('حدث خطأ في حفظ الصندوق');
  }
}

// وظائف التعديل والحذف للحرفاء
async function editCustomer(customerId) {
  try {
    const customer = await MarketDB.get('customers', customerId);
    if (!customer) {
      alert('لم يتم العثور على الحريف');
      return;
    }

    const content = `
      <form id="customer-form" onsubmit="handleCustomerUpdate(event, ${customerId})">
        <div class="form-group">
          <label for="customer-name">اسم الحريف:</label>
          <input type="text" id="customer-name" name="name" value="${customer.name}" required>
        </div>
        <div class="form-group">
          <label for="customer-phone">رقم الهاتف:</label>
          <input type="tel" id="customer-phone" name="phone" value="${customer.phone || ''}">
        </div>
        <div class="form-group">
          <label for="customer-address">العنوان:</label>
          <textarea id="customer-address" name="address" rows="3">${customer.address || ''}</textarea>
        </div>
        <div style="display: flex; gap: 10px; justify-content: flex-end;">
          <button type="submit" class="btn btn-success">تحديث</button>
          <button type="button" class="btn btn-secondary" onclick="closeModal()">إلغاء</button>
        </div>
      </form>
    `;
    openModal('تعديل الحريف', content);
  } catch (error) {
    console.error('خطأ في تحميل بيانات الحريف:', error);
    alert('حدث خطأ في تحميل بيانات الحريف');
  }
}

async function handleCustomerUpdate(event, customerId) {
  event.preventDefault();
  try {
    const formData = new FormData(event.target);
    const customerData = {
      id: customerId,
      name: formData.get('name').trim(),
      phone: formData.get('phone').trim(),
      address: formData.get('address').trim(),
      created_at: (await MarketDB.get('customers', customerId)).created_at
    };

    await MarketDB.update('customers', customerData);
    closeModal();
    await loadCustomersData();
    alert('تم تحديث الحريف بنجاح');
  } catch (error) {
    console.error('خطأ في تحديث الحريف:', error);
    alert('حدث خطأ في تحديث الحريف');
  }
}

async function deleteCustomer(customerId) {
  if (confirm('هل أنت متأكد من حذف هذا الحريف؟ سيتم حذف جميع مشترياته أيضاً.')) {
    try {
      // حذف جميع مشتريات الحريف أولاً
      const purchases = await MarketDB.getByIndex('purchases', 'customer_id', customerId);
      for (const purchase of purchases) {
        await MarketDB.delete('purchases', purchase.id);
      }

      // حذف الحريف
      await MarketDB.delete('customers', customerId);
      await loadCustomersData();
      alert('تم حذف الحريف بنجاح');
    } catch (error) {
      console.error('خطأ في حذف الحريف:', error);
      alert('حدث خطأ في حذف الحريف');
    }
  }
}

// وظائف التعديل والحذف للموردين
async function editSupplier(supplierId) {
  try {
    const supplier = await MarketDB.get('suppliers', supplierId);
    if (!supplier) {
      alert('لم يتم العثور على المورد');
      return;
    }

    const content = `
      <form id="supplier-form" onsubmit="handleSupplierUpdate(event, ${supplierId})">
        <div class="form-group">
          <label for="supplier-name">اسم المورد:</label>
          <input type="text" id="supplier-name" name="name" value="${supplier.name}" required>
        </div>
        <div class="form-group">
          <label for="supplier-phone">رقم الهاتف:</label>
          <input type="tel" id="supplier-phone" name="phone" value="${supplier.phone || ''}">
        </div>
        <div class="form-group">
          <label for="supplier-address">العنوان:</label>
          <textarea id="supplier-address" name="address" rows="3">${supplier.address || ''}</textarea>
        </div>
        <div style="display: flex; gap: 10px; justify-content: flex-end;">
          <button type="submit" class="btn btn-success">تحديث</button>
          <button type="button" class="btn btn-secondary" onclick="closeModal()">إلغاء</button>
        </div>
      </form>
    `;
    openModal('تعديل المورد', content);
  } catch (error) {
    console.error('خطأ في تحميل بيانات المورد:', error);
    alert('حدث خطأ في تحميل بيانات المورد');
  }
}

async function handleSupplierUpdate(event, supplierId) {
  event.preventDefault();
  try {
    const formData = new FormData(event.target);
    const supplierData = {
      id: supplierId,
      name: formData.get('name').trim(),
      phone: formData.get('phone').trim(),
      address: formData.get('address').trim(),
      created_at: (await MarketDB.get('suppliers', supplierId)).created_at
    };

    await MarketDB.update('suppliers', supplierData);
    closeModal();
    await loadSuppliersData();
    alert('تم تحديث المورد بنجاح');
  } catch (error) {
    console.error('خطأ في تحديث المورد:', error);
    alert('حدث خطأ في تحديث المورد');
  }
}

async function deleteSupplier(supplierId) {
  if (confirm('هل أنت متأكد من حذف هذا المورد؟ سيتم حذف جميع بضائعه ومشترياته أيضاً.')) {
    try {
      // حذف جميع البضائع والمشتريات المرتبطة بالمورد
      const products = await MarketDB.getByIndex('products', 'supplier_id', supplierId);
      for (const product of products) {
        await MarketDB.delete('products', product.id);
      }

      const purchases = await MarketDB.getByIndex('purchases', 'supplier_id', supplierId);
      for (const purchase of purchases) {
        await MarketDB.delete('purchases', purchase.id);
      }

      // حذف المورد
      await MarketDB.delete('suppliers', supplierId);
      await loadSuppliersData();
      alert('تم حذف المورد بنجاح');
    } catch (error) {
      console.error('خطأ في حذف المورد:', error);
      alert('حدث خطأ في حذف المورد');
    }
  }
}

// وظائف التعديل والحذف للصناديق
async function editBox(boxId) {
  try {
    const box = await MarketDB.get('boxes', boxId);
    if (!box) {
      alert('لم يتم العثور على الصندوق');
      return;
    }

    const content = `
      <form id="box-form" onsubmit="handleBoxUpdate(event, ${boxId})">
        <div class="form-group">
          <label for="box-name">اسم الصندوق:</label>
          <input type="text" id="box-name" name="name" value="${box.name}" required>
        </div>
        <div class="form-group">
          <label for="box-weight">الوزن الفارغ (كغ):</label>
          <input type="number" id="box-weight" name="weight_empty" step="0.1" min="0" value="${box.weight_empty}" required>
        </div>
        <div class="form-group">
          <label for="box-deposit">مبلغ الرهن (د.ت):</label>
          <input type="number" id="box-deposit" name="deposit_amount" step="0.1" min="0" value="${box.deposit_amount}" required>
        </div>
        <div class="form-group">
          <label for="box-load-cost">تكلفة الحمولة (د.ت):</label>
          <input type="number" id="box-load-cost" name="load_cost" step="0.01" min="0" value="${box.load_cost}" required>
        </div>
        <div style="display: flex; gap: 10px; justify-content: flex-end;">
          <button type="submit" class="btn btn-success">تحديث</button>
          <button type="button" class="btn btn-secondary" onclick="closeModal()">إلغاء</button>
        </div>
      </form>
    `;
    openModal('تعديل الصندوق', content);
  } catch (error) {
    console.error('خطأ في تحميل بيانات الصندوق:', error);
    alert('حدث خطأ في تحميل بيانات الصندوق');
  }
}

async function handleBoxUpdate(event, boxId) {
  event.preventDefault();
  try {
    const formData = new FormData(event.target);
    const boxData = {
      id: boxId,
      name: formData.get('name').trim(),
      weight_empty: parseFloat(formData.get('weight_empty')),
      deposit_amount: parseFloat(formData.get('deposit_amount')),
      load_cost: parseFloat(formData.get('load_cost'))
    };

    await MarketDB.update('boxes', boxData);
    closeModal();
    await loadBoxesData();
    alert('تم تحديث الصندوق بنجاح');
  } catch (error) {
    console.error('خطأ في تحديث الصندوق:', error);
    alert('حدث خطأ في تحديث الصندوق');
  }
}

async function deleteBox(boxId) {
  if (confirm('هل أنت متأكد من حذف هذا الصندوق؟')) {
    try {
      await MarketDB.delete('boxes', boxId);
      await loadBoxesData();
      alert('تم حذف الصندوق بنجاح');
    } catch (error) {
      console.error('خطأ في حذف الصندوق:', error);
      alert('حدث خطأ في حذف الصندوق');
    }
  }
}

// وظائف جديدة للتعامل مع جدول الحرفاء
function editCustomerFromTable(id) {
  const customer = customers.find(c => c.id === id);
  if (!customer) {
    alert('لم يتم العثور على الحريف');
    return;
  }

  const content = `
    <form id="customer-form" onsubmit="handleCustomerUpdateFromTable(event, ${id})">
      <div class="form-group">
        <label for="customer-name">اسم الحريف:</label>
        <input type="text" id="customer-name" name="name" value="${customer.name}" required>
      </div>
      <div class="form-group">
        <label for="customer-goods">نوع البضاعة:</label>
        <input type="text" id="customer-goods" name="goods" value="${customer.goods}" required>
      </div>
      <div class="form-group">
        <label for="customer-boxCount">عدد الصناديق:</label>
        <input type="number" id="customer-boxCount" name="boxCount" value="${customer.boxCount}" min="1" required>
      </div>
      <div class="form-group">
        <label for="customer-boxType">نوع الصناديق:</label>
        <select id="customer-boxType" name="boxType" required>
          <option value="صندوق كبير" ${customer.boxType === 'صندوق كبير' ? 'selected' : ''}>صندوق كبير</option>
          <option value="Plato" ${customer.boxType === 'Plato' ? 'selected' : ''}>Plato</option>
          <option value="Scarface" ${customer.boxType === 'Scarface' ? 'selected' : ''}>Scarface</option>
          <option value="صندوق متوسط" ${customer.boxType === 'صندوق متوسط' ? 'selected' : ''}>صندوق متوسط</option>
          <option value="صندوق صغير" ${customer.boxType === 'صندوق صغير' ? 'selected' : ''}>صندوق صغير</option>
        </select>
      </div>
      <div class="form-group">
        <label for="customer-deposit">الرهن (د.ت):</label>
        <input type="number" id="customer-deposit" name="deposit" value="${customer.deposit}" step="0.01" min="0" required>
      </div>
      <div class="form-group">
        <label for="customer-totalAmount">المبلغ الإجمالي (د.ت):</label>
        <input type="number" id="customer-totalAmount" name="totalAmount" value="${customer.totalAmount}" step="0.01" min="0" required>
      </div>
      <div class="form-group">
        <label for="customer-paidAmount">المبلغ المدفوع (د.ت):</label>
        <input type="number" id="customer-paidAmount" name="paidAmount" value="${customer.paidAmount}" step="0.01" min="0" required>
      </div>
      <div style="display: flex; gap: 10px; justify-content: flex-end;">
        <button type="submit" class="btn btn-success">تحديث</button>
        <button type="button" class="btn btn-secondary" onclick="closeModal()">إلغاء</button>
      </div>
    </form>
  `;
  openModal('تعديل الحريف', content);
}

function handleCustomerUpdateFromTable(event, customerId) {
  event.preventDefault();
  const formData = new FormData(event.target);

  const customerIndex = customers.findIndex(c => c.id === customerId);
  if (customerIndex === -1) {
    alert('لم يتم العثور على الحريف');
    return;
  }

  // تحديث البيانات
  customers[customerIndex] = {
    ...customers[customerIndex],
    name: formData.get('name').trim(),
    goods: formData.get('goods').trim(),
    boxCount: parseInt(formData.get('boxCount')),
    boxType: formData.get('boxType'),
    deposit: parseFloat(formData.get('deposit')),
    totalAmount: parseFloat(formData.get('totalAmount')),
    paidAmount: parseFloat(formData.get('paidAmount'))
  };

  closeModal();
  renderCustomers(customers);
  alert('تم تحديث الحريف بنجاح');
}

function deleteCustomerFromTable(id) {
  if (confirm('هل أنت متأكد من حذف هذا الحريف؟')) {
    const customerIndex = customers.findIndex(c => c.id === id);
    if (customerIndex !== -1) {
      customers.splice(customerIndex, 1);
      renderCustomers(customers);
      alert('تم حذف الحريف بنجاح');
    } else {
      alert('لم يتم العثور على الحريف');
    }
  }
}

function reportCustomer(id) {
  const customer = customers.find(c => c.id === id);
  if (!customer) {
    alert('لم يتم العثور على الحريف');
    return;
  }

  const remaining = customer.totalAmount - customer.paidAmount;
  const status = getPaymentStatus(customer);

  const reportContent = `
    <div style="text-align: right; line-height: 1.6;">
      <h3 style="color: #2c3e50; margin-bottom: 20px;">تقرير الحريف</h3>
      <p><strong>رقم الحريف:</strong> ${customer.id}</p>
      <p><strong>الاسم:</strong> ${customer.name}</p>
      <p><strong>نوع البضاعة:</strong> ${customer.goods}</p>
      <p><strong>عدد الصناديق:</strong> ${customer.boxCount}</p>
      <p><strong>نوع الصناديق:</strong> ${customer.boxType}</p>
      <hr style="margin: 15px 0;">
      <p><strong>الرهن:</strong> ${customer.deposit.toFixed(2)} د.ت</p>
      <p><strong>المبلغ الإجمالي:</strong> ${customer.totalAmount.toFixed(2)} د.ت</p>
      <p><strong>المبلغ المدفوع:</strong> ${customer.paidAmount.toFixed(2)} د.ت</p>
      <p><strong>المتبقي:</strong> ${remaining.toFixed(2)} د.ت</p>
      <p><strong>الحالة:</strong> <span style="color: ${status === 'مدفوع' ? '#28a745' : status === 'بالتقسيط' ? '#ffc107' : '#dc3545'};">${status}</span></p>
    </div>
    <div style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 20px;">
      <button class="btn btn-primary" onclick="printCustomerReport(${id})">
        <i class="fas fa-print"></i> طباعة
      </button>
      <button class="btn btn-secondary" onclick="closeModal()">إغلاق</button>
    </div>
  `;

  openModal('تقرير الحريف', reportContent);
}

// طباعة تقرير الحريف
function printCustomerReport(id) {
  const customer = customers.find(c => c.id === id);
  if (!customer) {
    alert('لم يتم العثور على الحريف');
    return;
  }

  const remaining = customer.totalAmount - customer.paidAmount;
  const status = getPaymentStatus(customer);

  const printContent = `
    <div style="text-align: center; margin-bottom: 30px;">
      <h1>سوق الجملة للخضر والغلال بجرزونة</h1>
      <h2>نقطة بيع عدد 14 - بيه الغالي</h2>
      <h3>تقرير الحريف</h3>
    </div>
    <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
      <tr><td style="border: 1px solid #ddd; padding: 8px;"><strong>رقم الحريف:</strong></td><td style="border: 1px solid #ddd; padding: 8px;">${customer.id}</td></tr>
      <tr><td style="border: 1px solid #ddd; padding: 8px;"><strong>الاسم:</strong></td><td style="border: 1px solid #ddd; padding: 8px;">${customer.name}</td></tr>
      <tr><td style="border: 1px solid #ddd; padding: 8px;"><strong>نوع البضاعة:</strong></td><td style="border: 1px solid #ddd; padding: 8px;">${customer.goods}</td></tr>
      <tr><td style="border: 1px solid #ddd; padding: 8px;"><strong>عدد الصناديق:</strong></td><td style="border: 1px solid #ddd; padding: 8px;">${customer.boxCount}</td></tr>
      <tr><td style="border: 1px solid #ddd; padding: 8px;"><strong>نوع الصناديق:</strong></td><td style="border: 1px solid #ddd; padding: 8px;">${customer.boxType}</td></tr>
      <tr><td style="border: 1px solid #ddd; padding: 8px;"><strong>الرهن:</strong></td><td style="border: 1px solid #ddd; padding: 8px;">${customer.deposit.toFixed(2)} د.ت</td></tr>
      <tr><td style="border: 1px solid #ddd; padding: 8px;"><strong>المبلغ الإجمالي:</strong></td><td style="border: 1px solid #ddd; padding: 8px;">${customer.totalAmount.toFixed(2)} د.ت</td></tr>
      <tr><td style="border: 1px solid #ddd; padding: 8px;"><strong>المبلغ المدفوع:</strong></td><td style="border: 1px solid #ddd; padding: 8px;">${customer.paidAmount.toFixed(2)} د.ت</td></tr>
      <tr><td style="border: 1px solid #ddd; padding: 8px;"><strong>المتبقي:</strong></td><td style="border: 1px solid #ddd; padding: 8px;">${remaining.toFixed(2)} د.ت</td></tr>
      <tr><td style="border: 1px solid #ddd; padding: 8px;"><strong>الحالة:</strong></td><td style="border: 1px solid #ddd; padding: 8px;">${status}</td></tr>
    </table>
    <p style="text-align: center; margin-top: 30px;">تاريخ الطباعة: ${new Date().toLocaleDateString('ar-TN')}</p>
  `;

    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
      <html>
      <head>
        <title>تقرير الحريف - ${customer.name}</title>
        <style>
          body { font-family: Arial, sans-serif; direction: rtl; margin: 20px; }
          table { width: 100%; border-collapse: collapse; }
          th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
          h1, h2, h3 { text-align: center; color: #333; }
        </style>
      </head>
      <body>
        ${printContent}
      </body>
      </html>
    `);
    printWindow.document.close();
    printWindow.print();
  } catch (error) {
    console.error('خطأ في طباعة التقرير:', error);
    alert('حدث خطأ في طباعة التقرير');
  }
}

// إعداد البحث في الحرفاء
function setupCustomerSearch() {
  const searchInput = document.getElementById("searchCustomer");
  if (searchInput) {
    searchInput.addEventListener("input", (e) => {
      const query = e.target.value.trim().toLowerCase();
      const filtered = customers.filter(c =>
        c.name.toLowerCase().includes(query) ||
        c.id.toString().includes(query) ||
        c.goods.toLowerCase().includes(query) ||
        c.boxType.toLowerCase().includes(query)
      );
      renderCustomers(filtered);
    });
  }
}

// إعداد نافذة التعديل
function setupEditModal() {
  // زر الإلغاء
  const cancelBtn = document.getElementById("cancelEditBtn");
  if (cancelBtn) {
    cancelBtn.addEventListener("click", () => {
      document.getElementById("editModal").classList.add("hidden");
    });
  }

  // زر الحفظ
  const saveBtn = document.getElementById("saveEditBtn");
  if (saveBtn) {
    saveBtn.addEventListener("click", () => {
      const customer = customers.find(c => c.id === selectedCustomerId);
      if (!customer) {
        alert('لم يتم العثور على الحريف');
        return;
      }

      // حفظ القيم القديمة للمقارنة
      const oldBoxCount = customer.boxCount;
      const oldPaidAmount = customer.paidAmount;

      // تحديث البيانات
      customer.boxCount = parseInt(document.getElementById("editBoxCount").value) || customer.boxCount;
      customer.boxType = document.getElementById("editBoxType").value || customer.boxType;
      customer.deposit = parseFloat(document.getElementById("editDeposit").value) || customer.deposit;

      // إضافة المبلغ الإضافي المدفوع
      const additionalPaid = parseFloat(document.getElementById("editPaidAmount").value);
      if (!isNaN(additionalPaid) && additionalPaid > 0) {
        customer.paidAmount += additionalPaid;
      }

      // إخفاء النافذة وإعادة عرض الجدول
      document.getElementById("editModal").classList.add("hidden");
      renderCustomers(customers);

      // رسالة تأكيد مفصلة
      let message = `تم تحديث بيانات الحريف "${customer.name}" بنجاح!\n\n`;
      if (customer.boxCount !== oldBoxCount) {
        message += `• عدد الصناديق: ${oldBoxCount} ← ${customer.boxCount}\n`;
      }
      if (additionalPaid > 0) {
        message += `• مبلغ إضافي مدفوع: ${additionalPaid.toFixed(2)} د.ت\n`;
        message += `• إجمالي المدفوع: ${oldPaidAmount.toFixed(2)} ← ${customer.paidAmount.toFixed(2)} د.ت\n`;
      }
      message += `• الحالة الجديدة: ${getPaymentStatus(customer)}`;

      alert(message);
    });
  }

  // إغلاق النافذة عند النقر خارجها
  const modal = document.getElementById("editModal");
  if (modal) {
    modal.addEventListener("click", (e) => {
      if (e.target === modal) {
        modal.classList.add("hidden");
      }
    });
  }
}
