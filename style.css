/* إعدادات عامة */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: #f5f6fa;
  color: #2c3e50;
  direction: rtl;
}

/* التخطيط الرئيسي */
#app {
  display: flex;
  height: 100vh;
}

/* القائمة الجانبية */
.sidebar {
  width: 280px;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: white;
  box-shadow: 2px 0 10px rgba(0,0,0,0.1);
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: 20px;
  text-align: center;
  border-bottom: 1px solid rgba(255,255,255,0.1);
}

.sidebar-header h2 {
  font-size: 1.5rem;
  margin-bottom: 5px;
  font-weight: 700;
}

.sidebar-header p {
  font-size: 0.9rem;
  opacity: 0.8;
}

.sidebar nav ul {
  list-style: none;
  padding: 20px 0;
}

.sidebar nav ul li {
  margin-bottom: 5px;
}

.sidebar nav ul li button {
  width: 100%;
  padding: 15px 25px;
  background: transparent;
  border: none;
  color: white;
  font-size: 1rem;
  text-align: right;
  cursor: pointer;
  border-radius: 0;
  transition: all 0.3s ease;
  border-right: 3px solid transparent;
  display: flex;
  align-items: center;
  gap: 15px;
}

.sidebar nav ul li button:hover {
  background-color: rgba(255,255,255,0.1);
  border-right-color: #74b9ff;
}

.sidebar nav ul li button.active {
  background-color: rgba(255,255,255,0.15);
  border-right-color: #00b894;
}

.sidebar nav ul li button i {
  font-size: 1.2rem;
  width: 20px;
}

/* المحتوى الرئيسي */
.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.main-header {
  background: white;
  padding: 20px 30px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.main-header h1 {
  color: #2c3e50;
  font-size: 1.8rem;
  font-weight: 600;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 15px;
}

.date-input {
  padding: 8px 12px;
  border: 2px solid #ddd;
  border-radius: 6px;
  font-family: inherit;
  direction: ltr;
}

.refresh-btn {
  background: #74b9ff;
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 6px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.refresh-btn:hover {
  background: #0984e3;
}

/* الصفحات */
.page {
  display: none;
  padding: 30px;
  overflow-y: auto;
  flex: 1;
}

.page.active {
  display: block;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.page-header h2 {
  color: #2c3e50;
  font-size: 1.8rem;
  font-weight: 600;
}

/* الكروت */
.cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.card {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  gap: 20px;
  transition: transform 0.3s ease;
}

.card:hover {
  transform: translateY(-5px);
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
}

.debt-card .card-icon {
  background: linear-gradient(135deg, #e17055, #d63031);
}

.pledge-card .card-icon {
  background: linear-gradient(135deg, #fdcb6e, #e17055);
}

.boxes-card .card-icon {
  background: linear-gradient(135deg, #74b9ff, #0984e3);
}

.sales-card .card-icon {
  background: linear-gradient(135deg, #00b894, #00a085);
}

.card-content h3 {
  font-size: 1rem;
  color: #636e72;
  margin-bottom: 8px;
}

.card-content p {
  font-size: 1.8rem;
  font-weight: 700;
  color: #2c3e50;
}

/* البحث */
.search-container {
  margin-bottom: 20px;
}

.search-box {
  position: relative;
  max-width: 400px;
}

.search-box i {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #636e72;
}

.search-box input {
  width: 100%;
  padding: 12px 45px 12px 15px;
  border: 2px solid #ddd;
  border-radius: 25px;
  font-family: inherit;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.search-box input:focus {
  outline: none;
  border-color: #74b9ff;
}

/* الجداول */
.data-table {
  width: 100%;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  overflow: hidden;
  border-collapse: collapse;
}

.data-table th,
.data-table td {
  padding: 15px 20px;
  text-align: right;
  border-bottom: 1px solid #dee2e6;
}

.data-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #495057;
}

.data-table tbody tr:hover {
  background: #f8f9fa;
}

/* الأزرار */
.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-family: inherit;
  font-weight: 500;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn-primary {
  background: #74b9ff;
  color: white;
}

.btn-primary:hover {
  background: #0984e3;
}

.btn-success {
  background: #00b894;
  color: white;
}

.btn-success:hover {
  background: #00a085;
}

.btn-danger {
  background: #e17055;
  color: white;
}

.btn-danger:hover {
  background: #d63031;
}

.btn-secondary {
  background: #636e72;
  color: white;
}

.btn-secondary:hover {
  background: #2d3436;
}

/* أزرار الإجراءات */
.action-buttons {
  display: flex;
  gap: 8px;
}

.action-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.action-btn.edit {
  background: #74b9ff;
  color: white;
}

.action-btn.edit:hover {
  background: #0984e3;
}

.action-btn.delete {
  background: #e17055;
  color: white;
}

.action-btn.delete:hover {
  background: #d63031;
}

/* النوافذ المنبثقة */
.modal-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.5);
  z-index: 1000;
  justify-content: center;
  align-items: center;
}

.modal-overlay.active {
  display: flex;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  padding: 20px;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  color: #2c3e50;
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #636e72;
}

.modal-body {
  padding: 20px;
}

/* النماذج */
.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #2c3e50;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 12px;
  border: 2px solid #ddd;
  border-radius: 6px;
  font-family: inherit;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #74b9ff;
}

/* حالات الدفع */
.status-paid {
  color: #00b894;
  font-weight: 600;
}

.status-partial {
  color: #fdcb6e;
  font-weight: 600;
}

.status-debt {
  color: #e17055;
  font-weight: 600;
}

/* الإعدادات */
.settings-container {
  display: grid;
  gap: 30px;
}

.setting-group {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.setting-group h3 {
  margin-bottom: 20px;
  color: #2c3e50;
}

.setting-item {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 15px;
}

.setting-item label {
  min-width: 150px;
  font-weight: 600;
}

/* تحسينات جدول الحرفاء */
#customersTable th, #customersTable td {
  text-align: center;
  padding: 10px;
}

#customersTable tbody tr:nth-child(even) {
  background: #f2f2f2;
}

#customersTable tbody tr:hover {
  background: #e8f4fd;
}

.action-btn {
  margin: 0 4px;
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.85rem;
  color: white;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.action-btn.edit {
  background-color: #28a745; /* أخضر */
}

.action-btn.delete {
  background-color: #dc3545; /* أحمر */
}

.action-btn.report {
  background-color: #007bff; /* أزرق */
}

.action-btn:hover {
  opacity: 0.8;
  transform: translateY(-1px);
}

/* حالات الدفع في الجدول */
.status-paid-table {
  background: #d4edda;
  color: #155724;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 600;
}

.status-partial-table {
  background: #fff3cd;
  color: #856404;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 600;
}

.status-debt-table {
  background: #f8d7da;
  color: #721c24;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 600;
}

/* نافذة التعديل الجديدة */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  padding: 30px;
  width: 400px;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.3);
  direction: rtl;
}

.modal-content h3 {
  margin-bottom: 20px;
  color: #2c3e50;
  text-align: center;
  font-size: 1.4rem;
}

.modal-content label {
  display: block;
  margin-bottom: 8px;
  margin-top: 15px;
  font-weight: 600;
  color: #2c3e50;
}

.modal-content input,
.modal-content select {
  width: 100%;
  padding: 12px;
  border: 2px solid #ddd;
  border-radius: 6px;
  font-family: inherit;
  font-size: 14px;
  margin-bottom: 10px;
  transition: border-color 0.3s ease;
}

.modal-content input:focus,
.modal-content select:focus {
  outline: none;
  border-color: #74b9ff;
}

.modal-content button {
  margin: 10px 5px 0 5px;
  padding: 12px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-family: inherit;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.3s ease;
}

#saveEditBtn {
  background: #00b894;
  color: white;
}

#saveEditBtn:hover {
  background: #00a085;
  transform: translateY(-1px);
}

#cancelEditBtn {
  background: #636e72;
  color: white;
}

#cancelEditBtn:hover {
  background: #2d3436;
  transform: translateY(-1px);
}

.hidden {
  display: none !important;
}

/* تجاوبية */
@media (max-width: 768px) {
  .sidebar {
    width: 250px;
    position: fixed;
    height: 100vh;
    z-index: 999;
    transform: translateX(100%);
    transition: transform 0.3s ease;
  }
  
  .sidebar.open {
    transform: translateX(0);
  }
  
  .content {
    margin-right: 0;
  }
  
  .cards {
    grid-template-columns: 1fr;
  }
  
  .page-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .main-header {
    padding: 15px 20px;
  }
  
  .page {
    padding: 20px;
  }
}
