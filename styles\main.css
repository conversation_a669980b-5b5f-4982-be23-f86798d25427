/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background-color: #f5f6fa;
    color: #2c3e50;
    direction: rtl;
}

/* الحاوي الرئيسي */
.app-container {
    display: flex;
    height: 100vh;
}

/* القائمة الجانبية */
.sidebar {
    width: 280px;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: white;
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
    position: fixed;
    height: 100vh;
    overflow-y: auto;
}

.sidebar-header {
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.sidebar-header h2 {
    font-size: 1.5rem;
    margin-bottom: 5px;
    font-weight: 700;
}

.sidebar-header p {
    font-size: 0.9rem;
    opacity: 0.8;
}

.sidebar-menu {
    list-style: none;
    padding: 20px 0;
}

.menu-item {
    padding: 15px 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    border-right: 3px solid transparent;
    display: flex;
    align-items: center;
    gap: 15px;
}

.menu-item:hover {
    background-color: rgba(255,255,255,0.1);
    border-right-color: #74b9ff;
}

.menu-item.active {
    background-color: rgba(255,255,255,0.15);
    border-right-color: #00b894;
}

.menu-item i {
    font-size: 1.2rem;
    width: 20px;
}

.menu-item span {
    font-weight: 500;
}

/* المحتوى الرئيسي */
.main-content {
    flex: 1;
    margin-right: 280px;
    display: flex;
    flex-direction: column;
}

.main-header {
    background: white;
    padding: 20px 30px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.main-header h1 {
    color: #2c3e50;
    font-size: 1.8rem;
    font-weight: 600;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.date-picker-container {
    display: flex;
    align-items: center;
    gap: 10px;
}

.date-input {
    padding: 8px 12px;
    border: 2px solid #ddd;
    border-radius: 6px;
    font-family: inherit;
    direction: ltr;
}

.refresh-btn {
    background: #74b9ff;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 6px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.refresh-btn:hover {
    background: #0984e3;
}

/* منطقة المحتوى */
.content-area {
    flex: 1;
    padding: 30px;
    overflow-y: auto;
}

.page {
    display: none;
}

.page.active {
    display: block;
}

/* كروت لوحة التحكم */
.dashboard-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.card {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: transform 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
}

.card-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.card:nth-child(1) .card-icon {
    background: linear-gradient(135deg, #e17055, #d63031);
}

.card:nth-child(2) .card-icon {
    background: linear-gradient(135deg, #fdcb6e, #e17055);
}

.card:nth-child(3) .card-icon {
    background: linear-gradient(135deg, #74b9ff, #0984e3);
}

.card:nth-child(4) .card-icon {
    background: linear-gradient(135deg, #00b894, #00a085);
}

.card-content h3 {
    font-size: 1rem;
    color: #636e72;
    margin-bottom: 8px;
}

.card-value {
    font-size: 1.8rem;
    font-weight: 700;
    color: #2c3e50;
}

/* الجداول */
.table-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    overflow: hidden;
}

.table-container h3 {
    padding: 20px 25px;
    background: #f8f9fa;
    margin: 0;
    border-bottom: 1px solid #dee2e6;
    color: #2c3e50;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 15px 20px;
    text-align: right;
    border-bottom: 1px solid #dee2e6;
}

.data-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.data-table tbody tr:hover {
    background: #f8f9fa;
}

/* الأزرار */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-family: inherit;
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: #74b9ff;
    color: white;
}

.btn-primary:hover {
    background: #0984e3;
}

.btn-success {
    background: #00b894;
    color: white;
}

.btn-success:hover {
    background: #00a085;
}

.btn-danger {
    background: #e17055;
    color: white;
}

.btn-danger:hover {
    background: #d63031;
}

.btn-secondary {
    background: #636e72;
    color: white;
}

.btn-secondary:hover {
    background: #2d3436;
}

/* النوافذ المنبثقة */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 15% auto;
    padding: 30px;
    border-radius: 12px;
    width: 400px;
    max-width: 90%;
    text-align: center;
}

.modal-content h3 {
    margin-bottom: 15px;
    color: #2c3e50;
}

.modal-content p {
    margin-bottom: 25px;
    color: #636e72;
}

.modal-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
}

/* حالات الدفع */
.status-paid {
    color: #00b894;
    font-weight: 600;
}

.status-partial {
    color: #fdcb6e;
    font-weight: 600;
}

.status-debt {
    color: #e17055;
    font-weight: 600;
}

/* رأس الصفحة */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.page-header h2 {
    color: #2c3e50;
    font-size: 1.8rem;
    font-weight: 600;
}

/* صندوق البحث */
.search-container {
    margin-bottom: 20px;
}

.search-box {
    position: relative;
    max-width: 400px;
}

.search-box i {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #636e72;
}

.search-box input {
    width: 100%;
    padding: 12px 45px 12px 15px;
    border: 2px solid #ddd;
    border-radius: 25px;
    font-family: inherit;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.search-box input:focus {
    outline: none;
    border-color: #74b9ff;
}

/* النماذج */
.form-modal {
    width: 500px;
    max-width: 90%;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #2c3e50;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 6px;
    font-family: inherit;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #74b9ff;
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

/* أزرار الإجراءات في الجداول */
.action-buttons {
    display: flex;
    gap: 8px;
}

.action-btn {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s ease;
}

.action-btn.edit {
    background: #74b9ff;
    color: white;
}

.action-btn.edit:hover {
    background: #0984e3;
}

.action-btn.delete {
    background: #e17055;
    color: white;
}

.action-btn.delete:hover {
    background: #d63031;
}

/* تجاوبية */
@media (max-width: 768px) {
    .sidebar {
        width: 250px;
        transform: translateX(100%);
        transition: transform 0.3s ease;
    }

    .sidebar.open {
        transform: translateX(0);
    }

    .main-content {
        margin-right: 0;
    }

    .dashboard-cards {
        grid-template-columns: 1fr;
    }

    .main-header {
        padding: 15px 20px;
    }

    .content-area {
        padding: 20px;
    }

    .page-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .action-buttons {
        flex-direction: column;
    }
}
