// وظائف مساعدة للنظام

// تنسيق التاريخ
function formatDate(date) {
    if (!date) return '';
    const d = new Date(date);
    return d.toLocaleDateString('ar-TN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
    });
}

// تنسيق التاريخ والوقت
function formatDateTime(date) {
    if (!date) return '';
    const d = new Date(date);
    return d.toLocaleString('ar-TN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// تنسيق المبلغ
function formatCurrency(amount) {
    if (amount === null || amount === undefined) return '0.00 د.ت';
    return parseFloat(amount).toFixed(2) + ' د.ت';
}

// تنسيق الوزن
function formatWeight(weight) {
    if (weight === null || weight === undefined) return '0.00 كغ';
    return parseFloat(weight).toFixed(2) + ' كغ';
}

// حساب الوزن الصافي
function calculateNetWeight(grossWeight, boxCount, boxWeight) {
    const gross = parseFloat(grossWeight) || 0;
    const count = parseInt(boxCount) || 0;
    const weight = parseFloat(boxWeight) || 0;
    return Math.max(0, gross - (count * weight));
}

// حساب تكلفة الحمولة
function calculateLoadCost(boxCount, loadCostPerBox) {
    const count = parseInt(boxCount) || 0;
    const cost = parseFloat(loadCostPerBox) || 0;
    return count * cost;
}

// حساب الرهن
function calculateDeposit(boxCount, depositPerBox) {
    const count = parseInt(boxCount) || 0;
    const deposit = parseFloat(depositPerBox) || 0;
    return count * deposit;
}

// حساب المبلغ الإجمالي
function calculateTotalAmount(netWeight, pricePerKg, loadCost) {
    const weight = parseFloat(netWeight) || 0;
    const price = parseFloat(pricePerKg) || 0;
    const load = parseFloat(loadCost) || 0;
    return (weight * price) + load;
}

// تحديد حالة الدفع
function getPaymentStatus(totalAmount, paidAmount) {
    const total = parseFloat(totalAmount) || 0;
    const paid = parseFloat(paidAmount) || 0;
    
    if (paid >= total) return 'paid';
    if (paid > 0) return 'partial';
    return 'debt';
}

// تنسيق حالة الدفع للعرض
function formatPaymentStatus(status) {
    const statusMap = {
        'paid': 'مدفوع',
        'partial': 'تقسيط',
        'debt': 'دين'
    };
    return statusMap[status] || 'غير محدد';
}

// الحصول على فئة CSS لحالة الدفع
function getPaymentStatusClass(status) {
    const classMap = {
        'paid': 'status-paid',
        'partial': 'status-partial',
        'debt': 'status-debt'
    };
    return classMap[status] || '';
}

// التحقق من صحة البيانات
function validateRequired(value, fieldName) {
    if (!value || value.toString().trim() === '') {
        throw new Error(`${fieldName} مطلوب`);
    }
    return true;
}

function validateNumber(value, fieldName, min = null, max = null) {
    const num = parseFloat(value);
    if (isNaN(num)) {
        throw new Error(`${fieldName} يجب أن يكون رقماً صحيحاً`);
    }
    if (min !== null && num < min) {
        throw new Error(`${fieldName} يجب أن يكون أكبر من أو يساوي ${min}`);
    }
    if (max !== null && num > max) {
        throw new Error(`${fieldName} يجب أن يكون أصغر من أو يساوي ${max}`);
    }
    return true;
}

// البحث الذكي
function smartSearch(items, searchTerm, fields) {
    if (!searchTerm || searchTerm.trim() === '') return items;
    
    const term = searchTerm.toLowerCase().trim();
    return items.filter(item => {
        return fields.some(field => {
            const value = item[field];
            if (value === null || value === undefined) return false;
            return value.toString().toLowerCase().includes(term);
        });
    });
}

// إنشاء خيارات للقوائم المنسدلة
function createSelectOptions(items, valueField, textField, placeholder = 'اختر...') {
    let options = `<option value="">${placeholder}</option>`;
    items.forEach(item => {
        options += `<option value="${item[valueField]}">${item[textField]}</option>`;
    });
    return options;
}

// تحديث عنصر HTML بأمان
function updateElement(elementId, content) {
    const element = document.getElementById(elementId);
    if (element) {
        element.innerHTML = content;
    }
}

// الحصول على تاريخ اليوم
function getTodayDate() {
    return new Date().toISOString().split('T')[0];
}

// الحصول على بداية ونهاية اليوم
function getDayRange(date) {
    const start = new Date(date);
    start.setHours(0, 0, 0, 0);
    
    const end = new Date(date);
    end.setHours(23, 59, 59, 999);
    
    return { start: start.getTime(), end: end.getTime() };
}

// حساب إحصائيات الحرفاء
async function calculateCustomersStats(customers) {
    const customersWithStats = [];
    
    for (const customer of customers) {
        try {
            const purchases = await MarketDB.getByIndex('purchases', 'customer_id', customer.id);
            
            let totalAmount = 0;
            let paidAmount = 0;
            
            purchases.forEach(purchase => {
                totalAmount += purchase.total_amount || 0;
                paidAmount += purchase.paid_amount || 0;
            });
            
            const remaining = totalAmount - paidAmount;
            const status = getPaymentStatus(totalAmount, paidAmount);
            
            customersWithStats.push({
                ...customer,
                totalAmount,
                paidAmount,
                remaining,
                status
            });
        } catch (error) {
            console.error(`خطأ في حساب إحصائيات الحريف ${customer.id}:`, error);
            customersWithStats.push({
                ...customer,
                totalAmount: 0,
                paidAmount: 0,
                remaining: 0,
                status: 'paid'
            });
        }
    }
    
    return customersWithStats;
}

// حساب إحصائيات الموردين
async function calculateSuppliersStats(suppliers) {
    const suppliersWithStats = [];
    
    for (const supplier of suppliers) {
        try {
            const products = await MarketDB.getByIndex('products', 'supplier_id', supplier.id);
            const purchases = await MarketDB.getByIndex('purchases', 'supplier_id', supplier.id);
            
            let totalSales = 0;
            purchases.forEach(purchase => {
                totalSales += purchase.total_amount || 0;
            });
            
            suppliersWithStats.push({
                ...supplier,
                productCount: products.length,
                totalSales
            });
        } catch (error) {
            console.error(`خطأ في حساب إحصائيات المورد ${supplier.id}:`, error);
            suppliersWithStats.push({
                ...supplier,
                productCount: 0,
                totalSales: 0
            });
        }
    }
    
    return suppliersWithStats;
}

// تحديث جدول الحرفاء
function updateCustomersTable(customers) {
    const tbody = document.getElementById('customers-table-body');
    if (!tbody) return;
    
    if (customers.length === 0) {
        tbody.innerHTML = '<tr><td colspan="8" style="text-align: center;">لا توجد حرفاء مسجلين</td></tr>';
        return;
    }
    
    let html = '';
    customers.forEach(customer => {
        const statusClass = getPaymentStatusClass(customer.status);
        const statusText = formatPaymentStatus(customer.status);
        
        html += `
            <tr>
                <td>${customer.name}</td>
                <td>${customer.phone || '-'}</td>
                <td>${customer.address || '-'}</td>
                <td>${formatCurrency(customer.totalAmount)}</td>
                <td>${formatCurrency(customer.paidAmount)}</td>
                <td>${formatCurrency(customer.remaining)}</td>
                <td><span class="${statusClass}">${statusText}</span></td>
                <td>
                    <div class="action-buttons">
                        <button class="action-btn edit" onclick="editCustomer(${customer.id})">
                            <i class="fas fa-edit"></i> تعديل
                        </button>
                        <button class="action-btn delete" onclick="deleteCustomer(${customer.id})">
                            <i class="fas fa-trash"></i> حذف
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });
    
    tbody.innerHTML = html;
}

// تحديث جدول الموردين
function updateSuppliersTable(suppliers) {
    const tbody = document.getElementById('suppliers-table-body');
    if (!tbody) return;
    
    if (suppliers.length === 0) {
        tbody.innerHTML = '<tr><td colspan="7" style="text-align: center;">لا توجد موردين مسجلين</td></tr>';
        return;
    }
    
    let html = '';
    suppliers.forEach(supplier => {
        html += `
            <tr>
                <td>${supplier.name}</td>
                <td>${supplier.phone || '-'}</td>
                <td>${supplier.address || '-'}</td>
                <td>${supplier.productCount}</td>
                <td>${formatCurrency(supplier.totalSales)}</td>
                <td>${formatDate(supplier.created_at)}</td>
                <td>
                    <div class="action-buttons">
                        <button class="action-btn edit" onclick="editSupplier(${supplier.id})">
                            <i class="fas fa-edit"></i> تعديل
                        </button>
                        <button class="action-btn delete" onclick="deleteSupplier(${supplier.id})">
                            <i class="fas fa-trash"></i> حذف
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });
    
    tbody.innerHTML = html;
}

// تحديث جدول الصناديق
function updateBoxesTable(boxes) {
    const tbody = document.getElementById('boxes-table-body');
    if (!tbody) return;
    
    if (boxes.length === 0) {
        tbody.innerHTML = '<tr><td colspan="5" style="text-align: center;">لا توجد صناديق مسجلة</td></tr>';
        return;
    }
    
    let html = '';
    boxes.forEach(box => {
        html += `
            <tr>
                <td>${box.name}</td>
                <td>${formatWeight(box.weight_empty)}</td>
                <td>${formatCurrency(box.deposit_amount)}</td>
                <td>${formatCurrency(box.load_cost)}</td>
                <td>
                    <div class="action-buttons">
                        <button class="action-btn edit" onclick="editBox(${box.id})">
                            <i class="fas fa-edit"></i> تعديل
                        </button>
                        <button class="action-btn delete" onclick="deleteBox(${box.id})">
                            <i class="fas fa-trash"></i> حذف
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });
    
    tbody.innerHTML = html;
}

// البحث في الحرفاء
async function searchCustomers() {
    const searchTerm = document.getElementById('customer-search').value;
    const customers = await MarketDB.getAll('customers');
    const customersWithStats = await calculateCustomersStats(customers);
    
    const filteredCustomers = smartSearch(customersWithStats, searchTerm, ['name', 'phone', 'address']);
    updateCustomersTable(filteredCustomers);
}

// البحث في الموردين
async function searchSuppliers() {
    const searchTerm = document.getElementById('supplier-search').value;
    const suppliers = await MarketDB.getAll('suppliers');
    const suppliersWithStats = await calculateSuppliersStats(suppliers);
    
    const filteredSuppliers = smartSearch(suppliersWithStats, searchTerm, ['name', 'phone', 'address']);
    updateSuppliersTable(filteredSuppliers);
}
